import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import ProductItem from '@/component/product/ProductItem';
import { MyListItem, useMyListStore, initialMyListState } from '../store';
import { WeeeProductView } from 'weee-native';

const MyListProductItem = ({item}: { item: MyListItem }) => {

  // const { isEditable } = useMyListStore((state) => 
  //     state.tabData?.[state.type] || initialMyListState.tabData[state.type]);
  // const { toggleSelectItem } = useMyListStore();

  const [productJson, setProductJson] = useState(JSON.stringify(item.product));

  return (
    <View className="flex-row items-center justify-between bg-white border-b border-gray-100 p-4 ">
      {/* {isEditable && (
        <TouchableOpacity
          style={styles.productCheckbox}
          onPress={() => toggleSelectItem(item.id)}
        >
          <View style={[styles.checkbox, item.isSelected && styles.checkedCheckbox]}>
            {item.isSelected && <Text style={styles.checkmark}>✓</Text>}
          </View>
        </TouchableOpacity>
      )} */}

      {item.itemType === 'product' && item.product && (
        // <ProductItem item={item.product} />
        <WeeeProductView productJson={productJson} style={{ width: "100%", height: 170 }} className='w-full h-44' /> 
      )}
      
    </View>
  );
};

const styles = StyleSheet.create({
  productCheckbox: {
    marginRight: 12,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#E5E5E5',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkedCheckbox: {
    backgroundColor: '#0A72BA',
    borderColor: '#0A72BA',
  },
  checkmark: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  }
});

export default MyListProductItem;