import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';

import { FlashList } from '@shopify/flash-list';
import { LoadingAnimation } from '@/component/loading';
import { <PERSON><PERSON>View } from 'moti';
import { Skeleton } from 'moti/skeleton';

import ContentLoader from 'react-content-loader';
import { AutoSkeletonView } from 'react-native-auto-skeleton';
import { SceneMap, TabView } from 'react-native-tab-view';

import { twMerge } from 'tailwind-merge';


import { Toast, Sync } from "weee-native";

import MyListProductItem from './MyListProductItem';

import { useTranslation } from 'react-i18next';

import WeeeIconFont from '@/component/common/IconFont';

// const MyLoader = () => (
//   <ContentLoader
//     height={140}
//     speed={1}
//     backgroundColor={'#333'}
//     foregroundColor={'#999'}
//     viewBox="0 0 380 70"
//   >
//     {/* Only SVG shapes */}
//     <rect x="0" y="0" rx="5" ry="5" width="70" height="70" />
//     <rect x="80" y="17" rx="4" ry="4" width="300" height="13" />
//     <rect x="80" y="40" rx="3" ry="3" width="250" height="10" />
//   </ContentLoader>
// )


function GoToCartButton () {
  const { t } = useTranslation();
  return (
      <View className="flex flex-row enki-button-secondary py-6 mx-8 mb-8 rounded-full justify-center items-center gap-x-2">
          <WeeeIconFont name="Shopping_cart1" className='text-primary-4' size={28} />
          <Text className={twMerge("text-white text-base", "enki-body-base-medium")}>{t('go_to_cart')}</Text>
      </View>
  ); 
}

import { initialMyListState, MyListItem, useMyListStore } from '../store';

const MyListSegmentProduct = () => {
  const { t } = useTranslation(['mylist', 'common']);

  const [selectedTab, setSelectedTab] = useState('');

  const { items, categories, isEmpty, isEditable, 
    queryParams, isSelectAll, removedIds } = useMyListStore((state) => 
    state.tabData?.[state.type] || initialMyListState.tabData[state.type]);
  
  const { type, fetch, loadMore, applyFilter,toggleSelectAll, removeSelected } = useMyListStore();

  useEffect(() => {
    fetch();
  }, []);

  useEffect(() => {
    if (removedIds.length > 0) {
      Toast.show(t("mylist_watchlist_edit_success"));
      Sync.collectStatusNotify(removedIds, "remove", "product")
    }
  }, [removedIds]);


  const renderTabSection = () => (
    <View>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tabScrollView}>
        {categories?.length > 0 && [{catalogue_name: t("common:all"), catalogue_num: ""}, ...categories].map((category) => (
          <TouchableOpacity
            key={category.catalogue_name}
            style={[styles.tab, selectedTab === category.catalogue_num && styles.activeTab]}
            onPress={() => {
              setSelectedTab(category.catalogue_num);
              // filterByCategory(category)
              applyFilter(category.catalogue_num);
            }
            }
          >
            <Text style={[styles.tabText, selectedTab === category.catalogue_num && styles.activeTabText]}>
              {category.catalogue_name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderSelectAllSection = () => (
    isEditable ? (
      <View style={styles.selectAllSection}>
        <TouchableOpacity style={styles.selectAllButton} onPress={() => toggleSelectAll()}>
          <View style={[styles.checkbox, isSelectAll && styles.checkedCheckbox]}>
            {isSelectAll && <Text style={styles.checkmark}>✓</Text>}
          </View>
          <Text style={styles.selectAllText}>Select All</Text>
        </TouchableOpacity>
      </View>
    ) : (
      <View />
    )
  );

  const Spacer = ({ height = 16 }) => <View style={{ height }} />;

  const colorMode = 'light';

  const motionSkeleton = () => (
    <View>
      <MotiView
        transition={{
          type: 'timing',
        }}
        style={{ backgroundColor: '#ffffff', borderRadius: 8 }}
        animate={{ backgroundColor: '#ffffff' }}
      >
        <Skeleton colorMode={colorMode} radius="round" height={75} width={75} />
        <Spacer />
        <Skeleton colorMode={colorMode} width={250} />
        <Spacer height={8} />
        <Skeleton colorMode={colorMode} width={'100%'} />
        <Spacer height={8} />
        <Skeleton colorMode={colorMode} width={'100%'} />
      </MotiView>
    </View>
  );

  const renderListItem = ({ item }: { item: MyListItem }) => {
    switch (item.itemType) {
      case "select":
        return renderSelectAllSection();
      case "product":
        return (
          <MyListProductItem
            item={item}
          />
        );
      default:
        return <View />;
    }
  };

  const renderFooter = () => {
    // load more animation
    if (queryParams.hasMore) {
      return (
        <View style={{ height: 50, justifyContent: 'center', alignItems: 'center' }}>
          <LoadingAnimation />
        </View>
      );
    }
    // 如果没有在加载，则不渲染任何东西
    return null;
  };

  const renderEmpty = () => {
    return <View style={{ height: "100%", justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ alignContent: 'center' }}>Empty</Text>
    </View>
  }

  if (isEmpty) {
    return renderEmpty();
  }

  return (
    <View style={styles.container}>
      {renderTabSection()}
      {/* <AutoSkeletonView isLoading={true}>
        {renderSelectAllSection()}
      </AutoSkeletonView> */}
      {/* {motionSkeleton()} */}

      <FlashList
        data={ [{ itemType: "select", id: BigInt(0) }, ...items] }
        renderItem={renderListItem}
        keyExtractor={(item, index) => index.toString()}
        style={styles.productList}
        showsVerticalScrollIndicator={false}
        onEndReached={() => {
          loadMore();
        }}
        onStartReached={() => {
          // Load products
        }}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}

      />

      {!(isEditable) ? (
        <GoToCartButton />
      ) : (
        <View style={styles.bottomButton}>
          <TouchableOpacity style={styles.goToCartButton} onPress={() => removeSelected()}>
            <Text style={styles.goToCartText}>Delete</Text>
          </TouchableOpacity>
        </View>
      )}

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  safeArea: {
    backgroundColor: '#FFFFFF',
  },
  tabSection: {
    backgroundColor: '#F8F8F8',
    paddingVertical: 12,
  },
  savedItemsSection: {
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  savedItemsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  savedItemsLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bookmarkIcon: {
    fontSize: 16,
    marginRight: 8,
  },

  tabScrollView: {
    paddingHorizontal: 16,
  },
  tab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  activeTab: {
    backgroundColor: '#0A72BA',
    borderColor: '#0A72BA',
  },
  tabText: {
    fontSize: 14,
    color: '#666666',
  },
  activeTabText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  selectAllSection: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  selectAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#E5E5E5',
    borderRadius: 4,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkedCheckbox: {
    backgroundColor: '#0A72BA',
    borderColor: '#0A72BA',
  },
  checkmark: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  selectAllText: {
    fontSize: 16,
    color: '#666666',
  },
  productList: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  
  bottomButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#F3F3F3',
  },
  goToCartButton: {
    backgroundColor: '#00BFFF',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  goToCartText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  shape: {
    justifyContent: 'center',
    height: 250,
    width: 250,
    borderRadius: 25,
    marginRight: 10,
    backgroundColor: 'white',
  },
  padded: {
    padding: 16,
  },
});

export default MyListSegmentProduct;