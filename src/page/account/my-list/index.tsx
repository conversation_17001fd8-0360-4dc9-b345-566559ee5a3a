import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
} from 'react-native';

import { SafeAreaProvider } from 'react-native-safe-area-context';

import WeeeIconFont from '@/component/common/IconFont';

import { Route } from 'weee-native';

import MyListTabSegment from './component/MyListTabSegment';

import { useTranslation } from 'react-i18next';

import { initialMyListState, useMyListStore } from './store';

const MyListPage = (props: any) => {

  const { t } = useTranslation("mylist");

  const { isEditable } = useMyListStore((state) => state.tabData[state.type] 
    || initialMyListState.tabData[state.type]);
  const { toggleEditable, changeTab, reset } = useMyListStore();

  useEffect(() => {
    changeTab(props.type)
    return () => {
      reset();
    }
  }, [props.type]);

  const renderHeader = () => (
    <View style={styles.safeArea}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={Route.pop}>
          <WeeeIconFont name="Back" />
        </TouchableOpacity>
        <Text className='enki-body-base-medium color-primary-1'>{t('mylist_header_title')}</Text>
        <TouchableOpacity onPress={() => toggleEditable()}>
          {isEditable ? (
            <Text style={styles.cancelButton}>Cancel</Text>
          ) : (
            <WeeeIconFont name="delete" />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaProvider style={styles.container}>
      {renderHeader()}
      <MyListTabSegment />
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  safeArea: {
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  backButton: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: 24,
    color: '#0A72BA',
    fontWeight: '300',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#0A72BA',
  },
  cancelButton: {
    fontSize: 16,
    color: '#0A72BA',
  },
  deleteIcon: {
    width: 24,
    height: 24,
  },
});

export default MyListPage;