import React from 'react';
import {View, StyleSheet, SafeAreaView} from 'react-native';
import AtcButtonList from '@/component/product/AtcButtonList';
import {ProductBean} from '@/api/model';

// 模拟产品数据
const mockProducts: ProductBean[] = [
  {
    id: 1001,
    name: '有机苹果',
    price: 4.99,
    volume_price: 4.49,
    volume_threshold: 3,
    min_order_quantity: 1,
    max_order_quantity: 10,
    product_key: 'apple_organic_001',
    category_name: '水果',
    is_pantry: false,
    is_limit_product: false,
    remaining_count: 50,
    volume_price_support: true,
    view_link: 'https://example.com/apple',
  },
  {
    id: 1002,
    name: '新鲜香蕉',
    price: 2.99,
    volume_price: 2.49,
    volume_threshold: 5,
    min_order_quantity: 1,
    max_order_quantity: 20,
    product_key: 'banana_fresh_002',
    category_name: '水果',
    is_pantry: false,
    is_limit_product: false,
    remaining_count: 100,
    volume_price_support: true,
    view_link: 'https://example.com/banana',
  },
  {
    id: 1003,
    name: '优质牛奶',
    price: 3.49,
    volume_price: 3.19,
    volume_threshold: 2,
    min_order_quantity: 1,
    max_order_quantity: 6,
    product_key: 'milk_premium_003',
    category_name: '乳制品',
    is_pantry: true,
    is_limit_product: false,
    remaining_count: 30,
    volume_price_support: true,
    view_link: 'https://example.com/milk',
  },
  {
    id: 1004,
    name: '全麦面包',
    price: 5.99,
    volume_price: 5.49,
    volume_threshold: 2,
    min_order_quantity: 1,
    max_order_quantity: 5,
    product_key: 'bread_wholemeal_004',
    category_name: '烘焙',
    is_pantry: true,
    is_limit_product: true,
    remaining_count: 15,
    volume_price_support: true,
    view_link: 'https://example.com/bread',
  },
  {
    id: 1005,
    name: '鸡蛋 (12个装)',
    price: 6.99,
    volume_price: 6.49,
    volume_threshold: 3,
    min_order_quantity: 1,
    max_order_quantity: 8,
    product_key: 'eggs_dozen_005',
    category_name: '蛋类',
    is_pantry: false,
    is_limit_product: false,
    remaining_count: 40,
    volume_price_support: true,
    view_link: 'https://example.com/eggs',
  },
];

const AtcButtonDemo = () => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <AtcButtonList products={mockProducts} />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
});

export default AtcButtonDemo;
