import React, {useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Alert,
} from 'react-native';
import {WeeeAtcButton} from 'weee-native';

// 简单的测试数据
const testProducts = [
  {
    id: 1001,
    name: '苹果',
    price: 4.99,
    product_key: 'apple_001',
  },
  {
    id: 1002,
    name: '香蕉',
    price: 2.99,
    product_key: 'banana_002',
  },
  {
    id: 1003,
    name: '牛奶',
    price: 3.49,
    product_key: 'milk_003',
  },
];

type AtcButtonRef = {
  expandWithNotification: () => void;
  collapse: () => void;
};

const AtcButtonTestScreen = () => {
  const [logs, setLogs] = useState<string[]>([]);
  const [quantities, setQuantities] = useState<{[key: number]: number}>({});
  
  // 存储每个WeeeAtcButton的引用
  const atcButtonRefs = useRef<{[key: number]: AtcButtonRef | null}>({});

  // 添加日志
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    setLogs(prev => [logMessage, ...prev.slice(0, 9)]); // 保留最新10条
  };

  // 构建产品数据
  const buildProductData = (product: typeof testProducts[0]) => ({
    id: product.id,
    name: product.name,
    price: product.price,
    product_key: product.product_key,
    min_order_quantity: 1,
    max_order_quantity: 99,
    volume_threshold: 3,
    category: 'Test',
    is_pantry: false,
    is_limit_product: false,
    remaining_count: 50,
    volume_price_support: false,
    productType: 'normal',
    referValue: '',
  });

  // 处理ATC事件
  const handleAtcEvent = (productId: number) => (type: string, qty: number) => {
    addLog(`Product ${productId}: ${type}, qty=${qty}`);
    
    if (type === 'update' || type === 'collapse') {
      setQuantities(prev => ({
        ...prev,
        [productId]: qty,
      }));
    }
  };

  // 手动展开产品
  const expandProduct = (productId: number) => {
    const atcRef = atcButtonRefs.current[productId];
    if (atcRef) {
      addLog(`手动展开产品 ${productId}`);
      atcRef.expandWithNotification();
    } else {
      addLog(`❌ 产品 ${productId} 的引用未找到`);
    }
  };

  // 手动收起产品
  const collapseProduct = (productId: number) => {
    const atcRef = atcButtonRefs.current[productId];
    if (atcRef) {
      addLog(`手动收起产品 ${productId}`);
      atcRef.collapse();
    } else {
      addLog(`❌ 产品 ${productId} 的引用未找到`);
    }
  };

  // 清空日志
  const clearLogs = () => {
    setLogs([]);
    addLog('日志已清空');
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <Text style={styles.title}>WeeeAtcButton 跨组件动画测试</Text>
        <Text style={styles.subtitle}>
          测试"展开一个组件时，其他已展开的组件自动收起"功能
        </Text>

        {/* 全局控制按钮 */}
        <View style={styles.globalControls}>
          <TouchableOpacity
            style={[styles.button, styles.primaryButton]}
            onPress={() => expandProduct(1001)}
          >
            <Text style={styles.buttonText}>展开苹果</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, styles.primaryButton]}
            onPress={() => expandProduct(1002)}
          >
            <Text style={styles.buttonText}>展开香蕉</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, styles.primaryButton]}
            onPress={() => expandProduct(1003)}
          >
            <Text style={styles.buttonText}>展开牛奶</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.globalControls}>
          <TouchableOpacity
            style={[styles.button, styles.secondaryButton]}
            onPress={() => {
              testProducts.forEach(product => collapseProduct(product.id));
            }}
          >
            <Text style={styles.buttonText}>收起所有</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, styles.secondaryButton]}
            onPress={clearLogs}
          >
            <Text style={styles.buttonText}>清空日志</Text>
          </TouchableOpacity>
        </View>

        {/* 产品列表 */}
        <View style={styles.productList}>
          {testProducts.map((product) => {
            const productData = buildProductData(product);
            const quantity = quantities[product.id] || 0;

            return (
              <View key={product.id} style={styles.productItem}>
                <View style={styles.productInfo}>
                  <Text style={styles.productName}>{product.name}</Text>
                  <Text style={styles.productPrice}>${product.price}</Text>
                  <Text style={styles.productQuantity}>数量: {quantity}</Text>
                </View>
                
                <View style={styles.atcContainer}>
                  <WeeeAtcButton
                    ref={(ref: AtcButtonRef | null) => {
                      atcButtonRefs.current[product.id] = ref;
                    }}
                    style={styles.atcButton}
                    productJson={JSON.stringify(productData)}
                    onUpdate={handleAtcEvent(product.id)}
                  />
                </View>
              </View>
            );
          })}
        </View>

        {/* 日志显示 */}
        <View style={styles.logContainer}>
          <Text style={styles.logTitle}>操作日志:</Text>
          {logs.map((log, index) => (
            <Text key={index} style={styles.logItem}>
              {log}
            </Text>
          ))}
          {logs.length === 0 && (
            <Text style={styles.emptyLog}>暂无日志</Text>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginVertical: 16,
    color: '#333',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
    paddingHorizontal: 16,
  },
  globalControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  button: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 80,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
  },
  secondaryButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  productList: {
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  productItem: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    color: '#333',
  },
  productPrice: {
    fontSize: 14,
    color: '#007AFF',
    marginBottom: 4,
  },
  productQuantity: {
    fontSize: 12,
    color: '#666',
  },
  atcContainer: {
    alignItems: 'center',
  },
  atcButton: {
    width: 120,
    height: 40,
  },
  logContainer: {
    backgroundColor: 'white',
    margin: 16,
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  logItem: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  emptyLog: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },
});

export default AtcButtonTestScreen;
