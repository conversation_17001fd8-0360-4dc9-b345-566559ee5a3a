import React, {useState, useRef, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {ProductBean} from '@/api/model';
import {WeeeAtcButton} from 'weee-native';

type AtcButtonListProps = {
  products: ProductBean[];
};

type AtcButtonRef = {
  expandWithNotification: () => void;
  collapse: () => void;
};

const AtcButtonList = ({products}: AtcButtonListProps) => {
  const [quantities, setQuantities] = useState<{[key: number]: number}>({});
  const [expandedProductId, setExpandedProductId] = useState<number | null>(null);
  
  // 存储每个WeeeAtcButton的引用
  const atcButtonRefs = useRef<{[key: number]: AtcButtonRef | null}>({});

  // 构建产品数据的辅助函数
  const buildProductData = useCallback((item: ProductBean) => ({
    id: item.id,
    name: item.name,
    price: item.price,
    volume_price: item.volume_price_support ? item.volume_price : undefined,
    volume_threshold: item.volume_threshold || 3,
    min_order_quantity: item.min_order_quantity || 1,
    max_order_quantity:
      item.max_order_quantity || item.product_max_order_quantity || 99,
    product_key: item.product_key,
    category: item.category_name || item.category || 'General',
    is_pantry: item.is_pantry || false,
    is_limit_product: item.is_limit_product || false,
    remaining_count: item.remaining_count || 50,
    volume_price_support: item.volume_price_support || false,
    productType: item.is_pantry ? 'pantry' : 'normal',
    referValue: item.view_link || '',
  }), []);

  // 处理WeeeAtcButton事件
  const handleAtcEvent = useCallback((productId: number) => (type: string, qty: number) => {
    console.log(`🎯 AtcButton event for product ${productId}:`, {type, qty});

    switch (type) {
      case 'update':
        // 更新数量状态
        setQuantities(prev => ({
          ...prev,
          [productId]: qty,
        }));

        // 根据CartOpLayout的状态判断是否展开
        // 注意：这里我们不能直接通过qty判断，因为CartOpLayout可能处于展开状态但qty为0
        // 我们需要通过其他方式来检测展开状态
        console.log(`📊 Product ${productId} quantity updated to: ${qty}`);
        break;

      case 'collapse':
        // 组件收起时的处理
        console.log(`📦 Product ${productId} collapsed with quantity: ${qty}`);
        setQuantities(prev => ({
          ...prev,
          [productId]: qty,
        }));

        // 清除展开状态
        if (expandedProductId === productId) {
          setExpandedProductId(null);
        }
        break;

      default:
        console.log(`❓ Unknown event type: ${type}`);
    }
  }, [expandedProductId]);

  // 手动触发展开某个产品的ATC按钮
  const expandProduct = useCallback((productId: number) => {
    console.log(`🚀 Manually expanding product ${productId}`);
    const atcRef = atcButtonRefs.current[productId];
    if (atcRef) {
      // 调用expandWithNotification方法，这会：
      // 1. 触发当前组件的展开动画
      // 2. 通知SharedOrderViewModel当前产品展开
      // 3. 其他已展开的AtcButton会自动收起
      atcRef.expandWithNotification();
      setExpandedProductId(productId);
      console.log(`✅ Triggered expansion for product ${productId}`);
    } else {
      console.warn(`❌ AtcButton ref not found for product ${productId}`);
    }
  }, []);

  // 手动收起某个产品的ATC按钮
  const collapseProduct = useCallback((productId: number) => {
    console.log(`📦 Manually collapsing product ${productId}`);
    const atcRef = atcButtonRefs.current[productId];
    if (atcRef) {
      atcRef.collapse();
      if (expandedProductId === productId) {
        setExpandedProductId(null);
      }
      console.log(`✅ Triggered collapse for product ${productId}`);
    } else {
      console.warn(`❌ AtcButton ref not found for product ${productId}`);
    }
  }, [expandedProductId]);

  // 渲染单个产品项
  const renderProductItem = ({item}: {item: ProductBean}) => {
    const productData = buildProductData(item);
    const quantity = quantities[item.id] || 0;
    const isExpanded = expandedProductId === item.id;

    return (
      <View style={styles.productItem}>
        <View style={styles.productInfo}>
          <Text style={styles.productName}>{item.name}</Text>
          <Text style={styles.productPrice}>${item.price}</Text>
          <Text style={styles.productQuantity}>数量: {quantity}</Text>
          <Text style={[
            styles.expandStatus,
            {color: isExpanded ? '#007AFF' : '#666'}
          ]}>
            {isExpanded ? '已展开' : '已收起'}
          </Text>
        </View>
        
        <View style={styles.buttonContainer}>
          {/* WeeeAtcButton组件 */}
          <WeeeAtcButton
            ref={(ref: AtcButtonRef | null) => {
              atcButtonRefs.current[item.id] = ref;
            }}
            style={styles.atcButton}
            productJson={JSON.stringify(productData)}
            onUpdate={handleAtcEvent(item.id)}
          />
          
          {/* 手动控制按钮 */}
          <View style={styles.controlButtons}>
            <TouchableOpacity
              style={[styles.controlButton, styles.expandButton]}
              onPress={() => expandProduct(item.id)}
            >
              <Text style={styles.buttonText}>展开</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.controlButton, styles.collapseButton]}
              onPress={() => collapseProduct(item.id)}
            >
              <Text style={styles.buttonText}>收起</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>ATC按钮列表演示</Text>
      <Text style={styles.subtitle}>
        展开一个按钮时，其他已展开的按钮会自动收起
      </Text>
      <Text style={styles.currentStatus}>
        当前展开的产品: {expandedProductId ? `#${expandedProductId}` : '无'}
      </Text>

      {/* 全局控制按钮 */}
      <View style={styles.globalControls}>
        <TouchableOpacity
          style={[styles.controlButton, styles.expandButton]}
          onPress={() => {
            // 展开第一个产品作为测试
            if (products.length > 0) {
              expandProduct(products[0].id);
            }
          }}
        >
          <Text style={styles.buttonText}>展开第一个</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.controlButton, styles.collapseButton]}
          onPress={() => {
            // 收起所有产品
            if (expandedProductId) {
              collapseProduct(expandedProductId);
            }
          }}
        >
          <Text style={styles.buttonText}>收起当前</Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={products}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderProductItem}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    textAlign: 'center',
  },
  currentStatus: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
    color: '#007AFF',
  },
  globalControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  listContainer: {
    paddingBottom: 20,
  },
  productItem: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 14,
    color: '#007AFF',
    marginBottom: 4,
  },
  productQuantity: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  expandStatus: {
    fontSize: 12,
    fontWeight: '500',
  },
  buttonContainer: {
    alignItems: 'center',
  },
  atcButton: {
    width: 120,
    height: 40,
    marginBottom: 8,
  },
  controlButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  controlButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    minWidth: 50,
  },
  expandButton: {
    backgroundColor: '#007AFF',
  },
  collapseButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default AtcButtonList;
