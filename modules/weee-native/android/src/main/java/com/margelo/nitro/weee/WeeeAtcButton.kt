package com.margelo.nitro.weee

import android.util.Log
import android.view.View
import com.facebook.react.uimanager.ThemedReactContext
import com.margelo.nitro.weee.data.AtcProductData
import com.margelo.nitro.weee.service.UIProvideManager
import com.margelo.nitro.weee.service.IAtcButtonProvider
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers

/**
 * Nitro ATC Button component implementation
 * Uses dependency injection to avoid direct coupling with main app classes
 */
class WeeeAtcButton(private var reactContext: ThemedReactContext?) : HybridWeeeAtcButtonSpec() {

    private val uiScope = CoroutineScope(Dispatchers.Main)
    private var productData: AtcProductData? = null
    private var _productJson: String? = null
    private val uiComponent: IAtcButtonProvider by lazy { UIProvideManager.createUIComponent(reactContext) }

    companion object {
        private const val TAG = "WeeeAtcButton"
    }

    init {
        try {
            // Initialize UI component
//            uiComponent.initialize(reactContext)
            setupUIEventHandlers()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize WeeeAtcButton", e)
        }
    }

    override var productJson: String?
        get() = _productJson
        set(value) {
            Log.d(TAG, "Setting productJson to: $value")
            _productJson = value
            value?.let {
//                uiComponent.initializeWithProductData(it)
                AtcProductData.fromJson(it)?.let{
                    productData = it
                    uiComponent.initializeWithProductData(it)
                }
            }
            view.post {
                uiComponent.rootView.requestLayout()
            }
        }
    override var quantity: Long?
        get() = 0
        set(value) {
//            uiComponent.setQuantity(value ?: 0)
        }

    private var eventCallback: ((type: String, qty: Double) -> Unit)? = null

    override var onUpdate: (type: String, qty: Double) -> Unit
        get() = eventCallback ?: { _,_, -> }
        set(value) {
            eventCallback = value
        }

    override fun collapse() {
//        uiComponent.animateCollapse()
    }

    /**
     * Setup UI event handlers
     */
    private fun setupUIEventHandlers() {
        val listener = object : IAtcButtonProvider.AtcOperationListener {

            override fun onUpdate() {
                eventCallback?.invoke("", 0.0)
            }

            override fun onCollapse(qty: Int) {
//                eventCallback?.invoke("", qty.toDouble())
                eventCallback?.invoke("", 0.0)
            }

            override fun getProductId(): Int {
                return productData?.id ?: 0
            }

            override fun getProductKey(): String {
                return productData?.productKey ?: ""
            }
        }

        uiComponent.setOperationListener(listener)
    }

    override val view: View
        get() = uiComponent.rootView

}
