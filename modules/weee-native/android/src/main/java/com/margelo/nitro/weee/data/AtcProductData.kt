package com.margelo.nitro.weee.data

/**
 * Simplified product data for ATC component
 * Replaces direct dependency on ProductBean from main app
 */
data class AtcProductData(
    // Required fields
    val id: Int,
    val name: String,
    val price: Double,
    val productKey: String,

    // Optional fields with defaults
    val volumePrice: Double = price,
    val volumeThreshold: Int = 0,
    val minOrderQuantity: Int = 1,
    val maxOrderQuantity: Int = 99,
    val category: String = "General",
    val isPantry: Boolean = false,
    val isLimitProduct: Boolean = false,
    val remainingCount: Int = 999,
    val volumePriceSupport: Boolean = false,
    val productType: String = "normal",
    val referValue: String = ""
) {
    companion object {
        /**
         * Create AtcProductData from JSON string
         */
        fun fromJson(jsonString: String): AtcProductData? {
            return try {
                // Parse JSON using simple key-value extraction
                val data = parseSimpleJson(jsonString)

                AtcProductData(
                    id = data["id"]?.toIntOrNull() ?: 0,
                    name = data["name"] ?: "",
                    price = data["price"]?.toDoubleOrNull() ?: 0.0,
                    productKey = data["product_key"] ?: data["productKey"] ?: "",
                    volumePrice = data["volume_price"]?.toDoubleOrNull() ?: data["price"]?.toDoubleOrNull() ?: 0.0,
                    volumeThreshold = data["volume_threshold"]?.toIntOrNull() ?: 0,
                    minOrderQuantity = data["min_order_quantity"]?.toIntOrNull() ?: 1,
                    maxOrderQuantity = data["max_order_quantity"]?.toIntOrNull() ?: 99,
                    category = data["category"] ?: "General",
                    isPantry = data["is_pantry"]?.toBoolean() ?: false,
                    isLimitProduct = data["is_limit_product"]?.toBoolean() ?: false,
                    remainingCount = data["remaining_count"]?.toIntOrNull() ?: 999,
                    volumePriceSupport = data["volume_price_support"]?.toBoolean() ?: false,
                    productType = data["productType"] ?: "normal",
                    referValue = data["referValue"] ?: ""
                )
            } catch (e: Exception) {
                null
            }
        }

        private fun parseSimpleJson(json: String): Map<String, String> {
            val result = mutableMapOf<String, String>()
            try {
                val cleanJson = json.trim().removePrefix("{").removeSuffix("}")
                val pairs = mutableListOf<String>()
                var current = ""
                var inQuotes = false
                var braceCount = 0

                for (char in cleanJson) {
                    when (char) {
                        '"' -> inQuotes = !inQuotes
                        '{' -> if (!inQuotes) braceCount++
                        '}' -> if (!inQuotes) braceCount--
                        ',' -> if (!inQuotes && braceCount == 0) {
                            pairs.add(current.trim())
                            current = ""
                            continue
                        }
                    }
                    current += char
                }
                if (current.trim().isNotEmpty()) {
                    pairs.add(current.trim())
                }

                for (pair in pairs) {
                    val colonIndex = pair.indexOf(':')
                    if (colonIndex > 0) {
                        val key = pair.substring(0, colonIndex).trim().removeSurrounding("\"")
                        val value = pair.substring(colonIndex + 1).trim().removeSurrounding("\"")
                        result[key] = value
                    }
                }
            } catch (e: Exception) {
                // Fallback to simple split if parsing fails
                val pairs = json.replace("{", "").replace("}", "").split(",")
                for (pair in pairs) {
                    val parts = pair.split(":", limit = 2)
                    if (parts.size == 2) {
                        val key = parts[0].trim().removeSurrounding("\"")
                        val value = parts[1].trim().removeSurrounding("\"")
                        result[key] = value
                    }
                }
            }
            return result
        }
    }

    /**
     * Convert to JSON string
     */
    fun toJson(): String {
        return """
        {
            "id": $id,
            "name": "$name",
            "price": $price,
            "product_key": "$productKey",
            "volume_price": $volumePrice,
            "volume_threshold": $volumeThreshold,
            "min_order_quantity": $minOrderQuantity,
            "max_order_quantity": $maxOrderQuantity,
            "category": "$category",
            "is_pantry": $isPantry,
            "is_limit_product": $isLimitProduct,
            "remaining_count": $remainingCount,
            "volume_price_support": $volumePriceSupport,
            "productType": "$productType",
            "referValue": "$referValue"
        }
        """.trimIndent()
    }
}
