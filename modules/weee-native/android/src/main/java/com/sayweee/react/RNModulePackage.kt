package com.sayweee.react

import com.facebook.react.BaseReactPackage
import com.facebook.react.bridge.NativeModule
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.module.model.ReactModuleInfo
import com.facebook.react.module.model.ReactModuleInfoProvider
import com.facebook.react.uimanager.ViewManager
import com.margelo.nitro.weee.views.HybridWeeeAtcButtonManager
import com.margelo.nitro.weee.views.HybridWeeeProductViewManager
import java.util.HashMap

class RNModulePackage : BaseReactPackage() {

  private val moduleCreators = mapOf<String, (ReactApplicationContext) -> NativeModule>(
  )

  override fun getModule(
    name: String,
    reactContext: ReactApplicationContext): NativeModule? {
    return moduleCreators[name]?.invoke(reactContext)
  }

  override fun getReactModuleInfoProvider(): ReactModuleInfoProvider {
    return ReactModuleInfoProvider {
      val moduleInfos: MutableMap<String, ReactModuleInfo> = HashMap()
      moduleCreators.keys.forEach { name ->
        addModule(moduleInfos, name)
      }
      moduleInfos
    }
  }

  private fun addModule(moduleInfos: MutableMap<String, ReactModuleInfo>, name: String) {
    moduleInfos[name] = ReactModuleInfo(
      name,
      name,
      false,  // canOverrideExistingModule
      false,  // needsEagerInit
      false,  // isCxxModule
      true // isTurboModule
    )
  }

  override fun createViewManagers(reactContext: ReactApplicationContext): List<ViewManager<in Nothing, in Nothing>> {
    val viewManagers = mutableListOf<ViewManager<in Nothing, in Nothing>>()
    viewManagers.add(HybridWeeeAtcButtonManager())
    viewManagers.add(HybridWeeeProductViewManager())
    return viewManagers
  }
}
