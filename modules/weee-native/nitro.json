{"cxxNamespace": ["weee"], "ios": {"iosModuleName": "WeeeNative"}, "android": {"androidNamespace": ["weee"], "androidCxxLibName": "WeeeNative"}, "autolinking": {"WeeeNetwork": {"swift": "WeeeNetwork", "kotlin": "WeeeNetwork"}, "WeeeCache": {"swift": "<PERSON><PERSON><PERSON><PERSON>", "kotlin": "<PERSON><PERSON><PERSON><PERSON>"}, "WeeeRoute": {"swift": "WeeeRoute", "kotlin": "WeeeRoute"}, "WeeeToast": {"swift": "WeeeToast", "kotlin": "WeeeToast"}, "WeeeSync": {"swift": "WeeeSync", "kotlin": "WeeeSync"}, "WeeeAtcButton": {"swift": "WeeeAtcButton", "kotlin": "WeeeAtcButton"}, "WeeeProductView": {"swift": "WeeeProductView", "kotlin": "WeeeProductView"}}}