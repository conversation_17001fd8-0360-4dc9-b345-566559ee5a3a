
import WeeeAtcButtonConfig from '../../nitrogen/generated/shared/json/WeeeAtcButtonConfig.json';

import {getHostComponent} from 'react-native-nitro-modules';

import {ViewStyle, View} from 'react-native';

import React, {forwardRef, useRef, useState} from 'react';


import type {
  AtcButtonProps,
  AtcButtonMethods,
} from '../nitro/WeeeAtcButton.nitro';

// Create the hybrid view components using getHostComponent
const WeeeAtcButtonNative = getHostComponent<AtcButtonProps, AtcButtonMethods>(
  'WeeeAtcButton',
  () => WeeeAtcButtonConfig,
);


// Define props interface for the wrapped component
export interface WeeeAtcButtonProps extends Omit<AtcButtonProps, 'style'> {
  className?: string;
  style?: ViewStyle;
}

// Wrapped component that supports NativeWind className
 const WeeeAtcButton = forwardRef<any, WeeeAtcButtonProps>(
  ({className, style, ...restProps}, ref) => {
    // 从 restProps 中排除 onListener，因为我们要自己处理
    const { onUpdate, ...props } = restProps;

    const atcRef = useRef<AtcButtonMethods | null>(null);

    const [refreshStatus, setRefreshStatus] = useState(false);
    const [refreshKey, setRefreshKey] = useState(0);

    const [ quantity, setqQuantity] = useState(0);

    // 创建事件监听器
    const eventListener = (type: string, qty: number) => {
      try {
        if (type == "collapse") {
          atcRef.current?.collapse();
        } else {
          // setRefreshKey(prevKey => prevKey + 1);
          
          setRefreshStatus(true);
          setTimeout(() => setRefreshStatus(false), 300);
          
          setqQuantity(qty)

        }
      } catch (error) {
        console.error('WeeeAtcButton event handling error:', error);
      }
    };


    if (className) {
      return (
        <View className={className} style={[style, refreshStatus && {opacity: 0.99}]}>
          <WeeeAtcButtonNative
            key={refreshKey}
            style={{flex: 1, ...style}}
            onUpdate={{ f: eventListener }}
            quantity = { BigInt(quantity) }
            hybridRef={{f: (ref) => {
              atcRef.current = ref;
            }}}
            {...props}
          />
        </View>
      );
    }
    return (
      <WeeeAtcButtonNative
        className={className}
        style={[style, refreshStatus && {opacity: 0.99}]}
        onUpdate={{ f: eventListener }}
        quantity = { BigInt(quantity) }
        {...props}
      />
    );
  },
);

WeeeAtcButton.displayName = 'WeeeAtcButton';

export default WeeeAtcButton;