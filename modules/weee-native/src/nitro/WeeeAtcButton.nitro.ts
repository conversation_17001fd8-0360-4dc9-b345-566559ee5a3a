import type {
  HybridView,
  HybridViewProps,
  HybridViewMethods,
} from 'react-native-nitro-modules';

export interface AtcButtonProps extends HybridViewProps {
  productJson?: string;
  quantity?: bigint;

  onUpdate: (type: string, qty: number) => void;
}

export interface AtcButtonMethods extends HybridViewMethods {
  collapse(): void
}

export type WeeeAtcButton = HybridView<AtcButtonProps, AtcButtonMethods>;
