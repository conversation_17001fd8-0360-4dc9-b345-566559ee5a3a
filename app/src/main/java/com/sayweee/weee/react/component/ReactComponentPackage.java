package com.sayweee.weee.react.component;


import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.facebook.react.BaseReactPackage;
import com.facebook.react.bridge.NativeModule;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.module.model.ReactModuleInfo;
import com.facebook.react.module.model.ReactModuleInfoProvider;
import com.facebook.react.uimanager.ViewManager;
import com.sayweee.weee.utils.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

//
// Created by <PERSON><PERSON> on 03/07/2025.
//
public class ReactComponentPackage extends BaseReactPackage {

    @NonNull
    @Override
    public List<ViewManager> createViewManagers(@NonNull ReactApplicationContext reactContext) {
        return CollectionUtils.arrayListOf(
                new ReactWeeeImageManager(reactContext)
                );
    }

    @Nullable
    @Override
    public NativeModule getModule(@NonNull String s, @NonNull ReactApplicationContext reactApplicationContext) {
        if (ReactWeeeImageManager.REACT_CLASS.equals(s)) {
            return new ReactWeeeImageManager(reactApplicationContext);
        }
        return null;
    }

    @NonNull
    @Override
    public ReactModuleInfoProvider getReactModuleInfoProvider() {
        return new ReactModuleInfoProvider() {
            @NonNull
            @Override
            public Map<String, ReactModuleInfo> getReactModuleInfos() {
                Map<String, ReactModuleInfo> map = new HashMap<>();
                map.put(ReactWeeeImageManager.REACT_CLASS, new ReactModuleInfo(
                        ReactWeeeImageManager.REACT_CLASS, // name
                        ReactWeeeImageManager.REACT_CLASS, // className
                        false,                           // canOverrideExistingModule
                        false,                           // needsEagerInit
                        false,                           // isCxxModule
                        true                             // isTurboModule
                ));

                return map;
            }
        };
    }
}
