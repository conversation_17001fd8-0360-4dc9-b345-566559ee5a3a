package com.sayweee.weee.react.atc;

import android.app.Activity;
import android.util.Log;
import android.view.View;

import androidx.activity.ComponentActivity;
import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;

import com.facebook.react.uimanager.ThemedReactContext;
import com.margelo.nitro.weee.data.AtcProductData;
import com.margelo.nitro.weee.service.IAtcButtonProvider;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.cart.bean.ProductBean;

import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.weee.widget.op.CartOpLayout;

import java.util.HashMap;
import java.util.Map;

/**
 * Adapter that wraps CartOpLayout to implement IAtcUIComponent interface
 * This bridges the main app's CartOpLayout with the nitro module's abstraction
 */
public class AtcButtonProvider implements IAtcButtonProvider {

    private CartOpLayout cartOpLayout;
    private IAtcButtonProvider.AtcOperationListener operationListener;

    @Override
    public View getRootView() {
        return cartOpLayout;
    }

    private boolean isRefresh = false;

    @Override
    public void initialize(ThemedReactContext context) {
        isRefresh = false;
        // Create CartOpLayout instance
        cartOpLayout = new CartOpLayout(context);
        cartOpLayout.setLayoutParams(new CartOpLayout.LayoutParams(200, 100));

        // Setup SharedOrderViewModel listener for cross-component state management
        setupSharedViewModelListener(context);

        cartOpLayout.setOnChangeListener(new CartOpLayout.OnChangeListener() {
            @Override
            public void onChange(int num) {
                if (operationListener != null) {
                    SimplePreOrderBean.ItemsBean item = OrderManager.get()
                            .getSimpleOrderItem(operationListener.getProductId(), operationListener.getProductKey());

                    operationListener.onCollapse(item != null ? item.quantity : 0);
                }
            }
        });

        // Note: setupInternalListeners() is called in initializeWithProductData()
        // after OpHelper.helperOp() to prevent the listener from being overwritten
    }

    /**
     * Setup SharedOrderViewModel listener to handle cross-component state synchronization
     * This enables the "expand current, collapse others" behavior in RN lists
     *
     * NOTE: CartOpLayout already has built-in SharedOrderViewModel listener in onAttachedToWindow()
     * We don't need to add another listener here, just ensure CartOpLayout is properly attached
     */
    private void setupSharedViewModelListener(ThemedReactContext context) {
        // CartOpLayout automatically handles SharedOrderViewModel listening in onAttachedToWindow()
        // Our job is to ensure the CartOpLayout is properly attached to the window
        // and that we trigger SharedOrderViewModel.productOpStatusData.postValue() at the right time
        Log.d("AtcButtonProvider", "CartOpLayout will auto-setup SharedOrderViewModel listener when attached to window");
    }

    @Override
    public void initializeWithProductData(AtcProductData product) {
        if (cartOpLayout != null && product != null) {
            try {
//                ProductBean productBean = JsonUtils.parseObject(productJson, ProductBean.class);
                // Convert AtcProductData to ProductBean
                ProductBean productBean = convertAtcProductDataToProductBean(product);

                if (productBean != null) {
                    // Call OpHelper.helperOp to set up the CartOpLayout properly
                    // This is the key missing piece that makes animations work
                    String source = "nitro_atc_component";
                    Map<String, Object> element = new HashMap<>();
                    Map<String, Object> ctx = new HashMap<>();

                    OpHelper.helperOp(cartOpLayout, productBean, productBean, source, element, ctx);

                    // IMPORTANT: Wrap the OpHelper listener instead of replacing it
                    // This preserves the animation and cart logic while adding our callbacks
                    setupWrappedListeners(productBean, source, element, ctx);

                    SimplePreOrderBean.ItemsBean item = OrderManager.get()
                            .getSimpleOrderItem(productBean.getProductId(), productBean.product_key);

                    if (item != null) {
                        Log.d("AtcButtonProvider", "Initializing product " + productBean.getProductId() +
                              " with quantity: " + item.quantity);

                        if (!isRefresh) {
                            // 首次初始化，不使用动画
                            isRefresh = true;
                            cartOpLayout.setOpNum(item.quantity);
                        } else {
                            // 后续更新，使用动画
                            cartOpLayout.setTextNumAnim(item.quantity);

                            // 如果数量大于0，通知其他组件收起
                            if (item.quantity > 0) {
                                cartOpLayout.post(() -> {
                                    if (cartOpLayout.getOpStatus() == CartOpLayout.OP_STATUS_EXPAND) {
                                        notifyExpansion();
                                    }
                                });
                            }
                        }
                    }
                }
            } catch (Exception e) {
                Log.e("CartOpLayoutUIComponent", "Error in initializeWithProductData", e);
            }
        }
    }

    @Override
    public void setQuantity(long lng) {
        if (cartOpLayout != null) {
            cartOpLayout.setTextNum((int) lng);
        }
    }

    @Override
    public void setOperationListener(AtcOperationListener listener) {
        this.operationListener = listener;
    }

    /**
     * Setup wrapped event listeners that preserve OpHelper logic while adding our callbacks
     * Since we can't access the original listener, we recreate the OpHelper logic here
     */
    private void setupWrappedListeners(ProductBean productBean, String source, Map<String, Object> element, Map<String, Object> ctx) {
        if (cartOpLayout == null) {
            return;
        }

        CartOpLayout.OnCartOpListener listener = cartOpLayout.getListener();
        CartOpLayout.OnCartOpListener wrappedListener = new CartOpLayout.OnCartOpListener() {

            @Override
            public void operateLeft(View view) {
                if (listener != null) {
                    listener.operateLeft(view);
                }
                if (operationListener != null) {
                    operationListener.onUpdate();
                }
            }

            @Override
            public void operateRight(View view) {
                Log.d("AtcButtonProvider", "operateRight called for product: " + getProductId());

                if (listener != null) {
                    listener.operateRight(view);
                }

                // Post a delayed check to ensure the operation has completed
                cartOpLayout.postDelayed(() -> {
                    int currentQty = cartOpLayout.getTextNum();
                    int opStatus = cartOpLayout.getOpStatus();

                    Log.d("AtcButtonProvider", "After operateRight - qty: " + currentQty +
                          ", status: " + opStatus + " (EXPAND=" + CartOpLayout.OP_STATUS_EXPAND + ")");

                    // If expanded, notify other components to collapse
                    if (opStatus == CartOpLayout.OP_STATUS_EXPAND) {
                        notifyExpansion();
                    }
                }, 50); // Small delay to ensure state is updated

                if (operationListener != null) {
                    operationListener.onUpdate();
                }
            }

            @Override
            public void onNumClick(View view) {
                Log.d("AtcButtonProvider", "onNumClick called for product: " + getProductId());

                if (listener != null) {
                    listener.onNumClick(view);
                }

                // Post a delayed check to ensure the operation has completed
                cartOpLayout.postDelayed(() -> {
                    int currentQty = cartOpLayout.getTextNum();
                    int opStatus = cartOpLayout.getOpStatus();

                    Log.d("AtcButtonProvider", "After onNumClick - qty: " + currentQty +
                          ", status: " + opStatus + " (EXPAND=" + CartOpLayout.OP_STATUS_EXPAND + ")");

                    // If expanded, notify other components to collapse
                    if (opStatus == CartOpLayout.OP_STATUS_EXPAND) {
                        notifyExpansion();
                    }
                }, 50); // Small delay to ensure state is updated

                if (operationListener != null) {
                    operationListener.onUpdate();
                }
            }

            @Override
            public int getProductId() {
                if (operationListener != null) {
                    return operationListener.getProductId();
                }
                if (productBean != null) {
                    return productBean.getProductId();
                }
                return 0;
            }
        };

        cartOpLayout.setOnOperateListener(wrappedListener);
    }

    /**
     * Notify SharedOrderViewModel about component expansion
     * This triggers collapse of other expanded components
     */
    private void notifyExpansion() {
        if (operationListener != null) {
            try {
                int productId = operationListener.getProductId();
                SharedOrderViewModel sharedViewModel = SharedOrderViewModel.get();
                sharedViewModel.productOpStatusData.postValue(productId);

                Log.d("AtcButtonProvider", "Notified expansion for product: " + productId);
            } catch (Exception e) {
                Log.e("AtcButtonProvider", "Failed to notify expansion", e);
            }
        }
    }

    private int getProductId() {
        return operationListener != null ? operationListener.getProductId() : -1;
    }

    /**
     * Get the original listener from CartOpLayout using reflection
     * This preserves the existing functionality while adding our custom listener
     */
    private CartOpLayout.OnCartOpListener getOriginalListener(CartOpLayout cartOpLayout) {
        try {
            // Use reflection to get the current listener
            java.lang.reflect.Field listenerField = CartOpLayout.class.getDeclaredField("listener");
            listenerField.setAccessible(true);
            Object listener = listenerField.get(cartOpLayout);

            if (listener instanceof CartOpLayout.OnCartOpListener) {
                return (CartOpLayout.OnCartOpListener) listener;
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Convert AtcProductData to ProductBean for OpHelper compatibility
     */
    private ProductBean convertAtcProductDataToProductBean(AtcProductData product) {
        try {
            ProductBean productBean = new ProductBean();
            productBean.id = product.getId();
            productBean.product_key = product.getProductKey();
            productBean.min_order_quantity = product.getMinOrderQuantity();
            productBean.max_order_quantity = product.getMaxOrderQuantity();
            productBean.volume_threshold = product.getVolumeThreshold();
            productBean.name = product.getName();
            productBean.price = product.getPrice();
            productBean.volume_price = product.getVolumePrice();
            productBean.product_type = product.getProductType();
//            productBean.refer_value = product.getReferValue();
            return productBean;
        } catch (Exception e) {
            Log.e("CartOpLayoutUIComponent", "Error converting AtcProductData to ProductBean", e);
            return null;
        }
    }

    @Override
    public void animateCollapse() {
        if (cartOpLayout != null) {
            cartOpLayout.collapseWithAnim();
        }
    }

    /**
     * Manually trigger expand animation and notify other components
     * This simulates the user clicking the add button to expand the component
     */
    public void expandWithNotification() {
        if (cartOpLayout != null && operationListener != null) {
            try {
                // Get current quantity and add 1 to trigger expansion
                int currentQty = cartOpLayout.getTextNum();
                int newQty = Math.max(1, currentQty + 1);

                Log.d("AtcButtonProvider", "Manually expanding product " +
                      operationListener.getProductId() + " with qty: " + newQty);

                // Use setTextNumAnim to trigger the expand animation
                cartOpLayout.setTextNumAnim(newQty);

                // Notify SharedOrderViewModel to trigger collapse of other components
                cartOpLayout.post(() -> {
                    if (cartOpLayout.getOpStatus() == CartOpLayout.OP_STATUS_EXPAND) {
                        notifyExpansion();
                    }
                });

            } catch (Exception e) {
                Log.e("AtcButtonProvider", "Failed to trigger manual expansion", e);
            }
        }
    }

    /**
     * Refresh the component state with animation
     * This should be called when the product data is updated externally
     */
    public void refreshWithAnimation() {
        if (cartOpLayout != null && operationListener != null) {
            try {
                // Get the latest quantity from OrderManager
                SimplePreOrderBean.ItemsBean item = OrderManager.get()
                        .getSimpleOrderItem(operationListener.getProductId(), operationListener.getProductKey());

                if (item != null) {
                    int currentDisplayQty = cartOpLayout.getTextNum();
                    int actualQty = item.quantity;

                    Log.d("AtcButtonProvider", "Refreshing product " + operationListener.getProductId() +
                          " from " + currentDisplayQty + " to " + actualQty);

                    if (currentDisplayQty != actualQty) {
                        // Use animation to update the quantity
                        cartOpLayout.setTextNumAnim(actualQty);

                        // If expanding (from 0 to >0), notify other components
                        if (currentDisplayQty == 0 && actualQty > 0) {
                            cartOpLayout.post(() -> {
                                if (cartOpLayout.getOpStatus() == CartOpLayout.OP_STATUS_EXPAND) {
                                    notifyExpansion();
                                }
                            });
                        }
                    }
                }
            } catch (Exception e) {
                Log.e("AtcButtonProvider", "Failed to refresh with animation", e);
            }
        }
    }
}
