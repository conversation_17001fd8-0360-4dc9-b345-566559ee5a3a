package com.sayweee.weee.widget.product;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.StrikethroughSpan;
import android.text.style.TextAppearanceSpan;
import android.util.AttributeSet;
import android.util.SparseIntArray;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.sayweee.logger.Logger;
import com.sayweee.service.ConfigService;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.CollectManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.account.AccountIntentCreator;
import com.sayweee.weee.module.ads.AdsManager;
import com.sayweee.weee.module.cart.bean.EntranceTag;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.LabelHelper;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cate.product.ProductIntentCreator;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.module.home.date.DateActivity;
import com.sayweee.weee.module.launch.service.ExperimentManager;
import com.sayweee.weee.module.search.v2.UserPersonalizationManager;
import com.sayweee.weee.module.search.v2.bean.SearchJsonField;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.BitmapUtils;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.weee.utils.spanner.DefaultURLClickListenerImpl;
import com.sayweee.weee.utils.support.DeviceUtils;
import com.sayweee.weee.widget.op.CartOpLayout;
import com.sayweee.weee.widget.op.CartStatusLayout;
import com.sayweee.weee.widget.tips.TipsBarManager;
import com.sayweee.weee.widget.tips.TipsBean;
import com.sayweee.widget.shape.ShapeTextView;
import com.sayweee.widget.shape.helper.ShapeHelper;
import com.sayweee.widget.tagflow.TagFlowLabelLayout;
import com.sayweee.widget.tagflow.TagFlowLayout;
import com.sayweee.widget.tagflow.bean.Label;
import com.sayweee.widget.tagflow.bean.LabelEntity;
import com.sayweee.widget.tagflow.bean.LabelEntityBuilder;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.utils.Spanny;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Author:  winds
 * Date:    2021/9/15.
 * Desc:
 */
public class ProductView extends LinearLayout {

    public static final int STYLE_CARD = 1;   //卡片
    public static final int STYLE_LIST = 2;   //列表

    public static final int STYLE_ITEM_SMALL = 3;  // 小号，组件有限制
    public static final int STYLE_ITEM_MINI = 4;  // 小号再压缩，组件有限制
    public static final int STYLE_ITEM_NORMAL = 5;  // 中号
    public static final int STYLE_ITEM_SEARCH_V2 = 6;

    protected static final int DYNAMIC_ITEM_BOGO = 1;
    protected static final int DYNAMIC_ITEM_MKPL_SHIPS_FROM = 2;
    protected static final int DYNAMIC_ITEM_MKPL_ETA_RANGE = 3;
    protected static final int DYNAMIC_ITEM_REMAINING = 4;
    protected static final int DYNAMIC_ITEM_SOLD_NUM = 5;
    protected static final int DYNAMIC_ITEM_TOP_X = 6;
    protected static final int DYNAMIC_ITEM_PRE_SALE_DELIVERY_DESC = 7;

    protected static final int[] DYNAMIC_ITEMS = new int[]{
            DYNAMIC_ITEM_PRE_SALE_DELIVERY_DESC, // Pre-sale delivery desc
            DYNAMIC_ITEM_BOGO, // BOGO - labelsView(iOS)
            DYNAMIC_ITEM_MKPL_SHIPS_FROM, // mkpl ships from - sold by (iOS)
            DYNAMIC_ITEM_TOP_X, // top_x - entrance_tag (iOS)
            DYNAMIC_ITEM_MKPL_ETA_RANGE, // mkpl eta_range - eta (iOS)
            DYNAMIC_ITEM_REMAINING, // remaining_tip - left (iOS)
            DYNAMIC_ITEM_SOLD_NUM, // sold_num - sold count (iOS)
    };

    private final SparseIntArray VISIBLE_ITEMS = CollectionUtils.intArrayOf(DYNAMIC_ITEMS);

    public String traceId;
    public boolean isShowMkplVendor = true; // show mkpl vendor name, default true
    private boolean isShowBugAgain; // show bug again tag, default false
    public int tagsDisplayCount;

    protected OnCollectClickCallback collectClickCallback;
    protected OnPdpClickCallback pdpClickCallback;

    private float productNameLabelBadgeScaleFactor = 1f;

    @IntDef({STYLE_CARD, STYLE_LIST, STYLE_ITEM_SMALL, STYLE_ITEM_MINI, STYLE_ITEM_NORMAL, STYLE_ITEM_SEARCH_V2})
    @Retention(RetentionPolicy.SOURCE)
    public @interface DisplayStyle {

    }

    protected ViewHelper helper;
    protected int displayStyle;

    public ProductView(Context context) {
        this(context, null);
    }

    public ProductView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ProductView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    public ProductView(Context context, int style) {
        super(context, null, 0);
        int layoutRes;
        if (style == STYLE_LIST) {
            layoutRes = R.layout.view_product_list;
        } else {
            layoutRes = R.layout.view_product_card;
        }
        View view = inflate(context, layoutRes, this);
        helper = new ViewHelper(view);
        this.displayStyle = style;
        applyFactorsIfSearchV2(style);
    }

    @SuppressWarnings("resource")
    protected void init(Context context, @Nullable AttributeSet attrs) {
        int style = STYLE_CARD;
        if (attrs != null) {
            TypedArray ta = context.getTheme().obtainStyledAttributes(attrs, R.styleable.ProductView, 0, 0);
            style = ta.getInt(R.styleable.ProductView_product_display_style, STYLE_CARD);
            ta.recycle();
        }
        int layoutRes;
        if (style == STYLE_LIST) {
            layoutRes = R.layout.view_product_list;
        } else {
            layoutRes = R.layout.view_product_card;
        }
        View view = inflate(context, layoutRes, this);
        helper = new ViewHelper(view);
        this.displayStyle = style;
        applyFactorsIfSearchV2(style);
    }

    public void setShowMkplVendor(boolean showMkplVendor) {
        isShowMkplVendor = showMkplVendor;
    }

    public void setShowBuyAgain(boolean showBugAgain) {
        isShowBugAgain = showBugAgain;
    }

    public void setCollectClickCallback(OnCollectClickCallback collectClickCallback) {
        this.collectClickCallback = collectClickCallback;
    }

    public void setPdpClickCallback(OnPdpClickCallback pdpClickCallback) {
        this.pdpClickCallback = pdpClickCallback;
    }

    public CartOpLayout getCartOpLayout() {
        return helper.getView(R.id.layout_op);
    }

    public ProductView setAttachedProduct(ProductBean bean, @DisplayStyle int style, @NonNull String source, Map<String, Object> element, Map<String, Object> ctx) {
        return setAttachedProduct(bean, style, source, null, element, ctx);
    }

    public ProductView setAttachedProduct(ProductBean bean, @DisplayStyle int style, @NonNull OnOpCallback callback) {
        return setAttachedProduct(bean, style, null, callback, null, null);
    }

    protected ProductView setAttachedProduct(ProductBean bean, @DisplayStyle int style, String source, OnOpCallback callback, Map<String, Object> element, Map<String, Object> ctx) {
        this.displayStyle = style;
        // buy again
        prependBuyAgainIfNeeded(bean);
        // ads
        if (bean.ads_creative != null) {
            AdsManager.setTrackImpressionFlag(bean, false);
        }

        Context context = getContext();

        // image
        applyProductImage(bean);
        // badge
        applyProductBadge(bean);
        // collect
        applyProductCollect(bean);
        // brand name
        applyBrandName(bean);
        // name
        applyProductName(bean);
        // price
        applyProductPrice(bean);
        // special tag
        applyProductSpecialTag(bean);

        // dynamic display items
        int displayCount = applyProductDynamicItems(bean);
        tagsDisplayCount = displayCount;

        if (style == STYLE_ITEM_SEARCH_V2 && displayCount > 0) {
            ((TextView) helper.getView(R.id.tv_product_name)).setMaxLines(4);
        }

        // status & product op
        boolean showSoldStatus = true;
        CartOpLayout layoutOp = getCartOpLayout();
        helper.setOnClickListener(R.id.layout_status, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                //12.9 已售罄状态不能取消提醒
                if (CollectManager.get().isProductCollect(bean.getProductId()) && isSoldOut(bean.sold_status)) {
                    if (style == STYLE_ITEM_SEARCH_V2) {
                        toWebPage(Constants.Url.COLLECT);
                    }
                    return;
                }
                onStatusClick(v, bean, style);
            }
        });

        boolean isLayoutStatusClickable = true;
        helper.setVisible(R.id.tv_sold_out, false);
        CartStatusLayout layoutStatus = helper.getView(R.id.layout_status);
        if ((bean.isBundle() || bean.isHotDish())) {
            int titleResId = R.string.s_view;
            ProductViewHelper.setProductStatus(layoutStatus, titleResId, 0, R.color.color_atc_mini_fg_default);
            helper.setVisible(R.id.tv_sold_out, isSoldOut(bean.sold_status));
            helper.setText(R.id.tv_sold_out, R.string.shopping_list_sold_out);
            helper.setVisible(R.id.iv_collect, false);
        } else if (OrderManager.get().isReachLimit(bean)) {
            isLayoutStatusClickable = false;
            ProductViewHelper.setProductStatusPurchased(layoutStatus);
        } else if (isChangeOtherDay(bean.sold_status)) {
            ProductViewHelper.setProductStatusChangeDate(layoutStatus);
        } else if (isSoldOut(bean.sold_status)) {
            helper.setVisible(R.id.iv_collect, false);
            helper.setVisible(R.id.tv_sold_out, true);
            boolean hasRestockTip = !EmptyUtils.isEmpty(bean.restock_tip);
            if (style == STYLE_CARD) {
                helper.setText(R.id.tv_sold_out, new Spanny(context.getString(R.string.shopping_list_sold_out)).append(hasRestockTip ? "\n" + bean.restock_tip : "", new AbsoluteSizeSpan(14, true)));
            }
            displaySoldStatus(bean, style);
        } else if (isPreSell(bean.sold_status)) {
            showSoldStatus = false;
            layoutOp.setDisableStyle();
        } else {
            showSoldStatus = false;
            if (callback != null) {
                callback.onOp(layoutOp, bean);
            } else {
                OpHelper.helperOp(layoutOp, bean, bean, source, element, ctx);
            }
        }

        if (style == STYLE_ITEM_SEARCH_V2) {
            layoutStatus.setClickable(isLayoutStatusClickable);
        }
        ViewTools.setViewVisibilityIfChanged(layoutStatus, showSoldStatus);
        ViewTools.setViewVisibilityIfChanged(layoutOp, !showSoldStatus);

        helper.setOnClickListener(R.id.layout_product, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (pdpClickCallback != null) {
                    pdpClickCallback.onPdpClick();
                } else {
                    onProductClick(context, bean);
                }
            }
        });

        return this;
    }

    private void prependBuyAgainIfNeeded(ProductBean bean) {
        boolean showBuyAgain = (isShowBugAgain || (displayStyle == STYLE_ITEM_SEARCH_V2))
                && UserPersonalizationManager.get().isBought(String.valueOf(bean.id));
        if (showBuyAgain) {
            EntranceTag buyAgainTag = null;
            if (bean.product_tag_list == null) {
                bean.product_tag_list = new ArrayList<>();
            } else {
                for (EntranceTag productTag : bean.product_tag_list) {
                    if (SearchJsonField.Values.WEEE_TAG_BUY_AGAIN.equals(productTag.tag_key)) {
                        buyAgainTag = productTag;
                    }
                }
            }
            if (buyAgainTag == null) {
                Context context = getContext();
                buyAgainTag = new EntranceTag();
                buyAgainTag.tag_key = SearchJsonField.Values.WEEE_TAG_BUY_AGAIN;
                buyAgainTag.tag_name = context.getString(R.string.s_buy_it_again).toUpperCase();
                int tagColor = context.getColor(R.color.search_results_row_v2_badge_buy_again_bg_color);
                buyAgainTag.tag_color = "#" + Integer.toHexString(tagColor);
                int tagFontColor = context.getColor(R.color.search_results_row_v2_badge_buy_again_font_color);
                buyAgainTag.tag_font_color = "#" + Integer.toHexString(tagFontColor);
                bean.product_tag_list.add(buyAgainTag);
            }
        }
    }

    protected void applyProductImage(ProductBean bean) {
        ImageView imageView = helper.getView(R.id.iv_icon);
        boolean imgLoaded = false;
        boolean shouldLoadB64Image = Constants.SearchV2.USE_B64_PLACEHOLDER
                && bean.is_search_v2
                && !EmptyUtils.isEmpty(bean.img_b64)
                && !EmptyUtils.isEmpty(bean.img_filter)
                && !bean.img_cached
                && DeviceUtils.isHighPerformanceDevice(getContext());
        if (shouldLoadB64Image) {
            try {
                Bitmap b64Bitmap = BitmapUtils.decodeB64(getContext(), bean.img_b64);
                Drawable imgB64Placeholder = new BitmapDrawable(getResources(), b64Bitmap);
                ImageLoader.load(
                        getContext(),
                        imageView,
                        WebpManager.get().getConvertUrl(ImageSpec.SPEC_PRODUCT, bean.getHeadImageUrl()),
                        imgB64Placeholder
                );
                bean.img_cached = true;
                imgLoaded = true;
            } catch (Throwable e) {
                Logger.e(e);
            }
        }
        if (!imgLoaded) {
            ImageLoader.load(
                    getContext(),
                    imageView,
                    WebpManager.get().getConvertUrl(ImageSpec.SPEC_PRODUCT, bean.getHeadImageUrl()),
                    R.mipmap.iv_product_placeholder
            );
        }
    }

    protected void applyBrandName(ProductBean bean) {
        TextView tvBrandName = helper.getView(R.id.tv_brand_name);
        if (tvBrandName != null) {
            boolean showSponsored = bean.is_sponsored && !EmptyUtils.isEmpty(bean.sponsored_text);
            boolean showBrand = !EmptyUtils.isEmpty(bean.brand_name);
            tvBrandName.setText(showSponsored ? bean.sponsored_text : bean.brand_name);
            tvBrandName.setVisibility(showSponsored || showBrand ? VISIBLE : GONE);
        }
    }

    protected void applyProductName(ProductBean bean) {
        TextView tvProductName = helper.getView(R.id.tv_product_name);
        if (ProductViewHelper.isDisplayRestricted(displayStyle)) {
            tvProductName.setLines(2);
        }
        tvProductName.setMaxLines(ProductViewHelper.getProductNameMaxLines(displayStyle));
        LabelHelper.setTitleLabel(tvProductName, bean, productNameLabelBadgeScaleFactor, new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                if (displayStyle == STYLE_ITEM_SEARCH_V2) {
                    helper.getView(R.id.layout_product).callOnClick();
                } else {
                    onProductClick(v.getContext(), bean);
                }
            }
        });
    }

    protected void applyProductCollect(ProductBean bean) {
        boolean isVisible = ProductViewHelper.shouldShowProductCollect(displayStyle);
        View ivCollect = helper.getView(R.id.iv_collect);
        ProductViewHelper.setProductCollect(ivCollect, CollectManager.get().isProductCollect(bean.id));
        ViewTools.setViewOnSafeClickListener(ivCollect, v -> {
            onCollectClick(v, bean);
            if (collectClickCallback != null) {
                collectClickCallback.onCollectClick();
            }
        });
        ViewTools.setViewVisibilityIfChanged(ivCollect, isVisible);
    }

    protected void applyProductPrice(ProductBean bean) {
        Context context = getContext();
        View layoutVolume = helper.getView(R.id.layout_volume);
        TextView tvPriceVolume = helper.getView(R.id.tv_price_volume);
        TextView tvPriceSingle = helper.getView(R.id.tv_price_single);
        TextView tvPrice = helper.getView(R.id.tv_price);
        TextView tvBasePrice = helper.getView(R.id.tv_price_delete);
        boolean showBasePrice = bean.base_price > 0;
        if (bean.showVolumePrice()) { // Volume Pricing
            ViewTools.setViewVisibilityIfChanged(layoutVolume, true);
            ViewTools.setViewVisibilityIfChanged(tvPrice, false);
            ViewTools.setViewVisibilityIfChanged(tvBasePrice, false);
            if (tvPriceVolume != null) {
                TextAppearanceSpan numeralAppearance = new TextAppearanceSpan(context, R.style.style_fluid_root_numeral_base);
                if (displayStyle == STYLE_ITEM_SEARCH_V2) {
                    int textSize = (int) (numeralAppearance.getTextSize() * Constants.SearchV2.PRODUCT_CARD_SIZE_FACTOR_TO_SHOW_MORE_PRODUCTS);
                    numeralAppearance = new TextAppearanceSpan(context, R.style.SearchProductView_VolumePriceValueTextAppearance);
                    numeralAppearance = new TextAppearanceSpan(
                            numeralAppearance.getFamily(),
                            Typeface.BOLD,
                            textSize,
                            numeralAppearance.getTextColor(),
                            numeralAppearance.getLinkTextColor()
                    );
                }
                Spanny s = new Spanny()
                        .append(OrderHelper.formatUSMoney(bean.price), numeralAppearance)
                        .append(context.getString(R.string.s_volume_threshold_simple, bean.volume_threshold));
                tvPriceVolume.setText(s);
            }
            // Volume single price
            if (tvPriceSingle != null) {
                Spanny spanny;
                if (showBasePrice) {
                    spanny = new Spanny(OrderHelper.formatUSMoney(bean.base_price), new StrikethroughSpan()).append(" ");
                } else {
                    spanny = new Spanny();
                }
                spanny.append(context.getString(R.string.s_volume_threshold_one_qty, OrderHelper.formatUSMoney(bean.volume_price)));
                tvPriceSingle.setText(spanny);
            }
        } else {
            ViewTools.setViewVisibilityIfChanged(layoutVolume, false);
            ViewTools.setViewVisibilityIfChanged(tvPrice, true);
            ViewTools.setViewVisibilityIfChanged(tvBasePrice, showBasePrice);
            // price
            helper.setText(R.id.tv_price, OrderHelper.formatUSMoney(bean.price));
            if (showBasePrice) {
                helper.setText(R.id.tv_price_delete, new Spanny(OrderHelper.formatUSMoney(bean.base_price), new StrikethroughSpan()));
            }
        }
    }

    protected void applyProductSpecialTag(ProductBean bean) {
        TextView tvSpecialTag = helper.getView(R.id.tv_special_tag);
        // 仅列表下有此属性
        if (displayStyle != STYLE_LIST || tvSpecialTag == null) {
            return;
        }
        // search list独有属性 :bundle or hotdish
        if (bean.isHotDish()) {
            tvSpecialTag.setText(R.string.s_restaurant_status);
            ViewTools.setViewVisibilityIfChanged(tvSpecialTag, true);
        } else if (bean.isBundle()) {
            tvSpecialTag.setText(R.string.s_bundle_status);
            ViewTools.setViewVisibilityIfChanged(tvSpecialTag, true);
        } else {
            ViewTools.setViewVisibilityIfChanged(tvSpecialTag, false);
        }
    }

    protected int applyProductDynamicItems(ProductBean bean) {
        int displayMax = ProductViewHelper.getMaxDisplayItems(displayStyle);
        boolean limitTags = ProductViewHelper.shouldLimitTags(displayStyle);
        return applyProductDynamicItems(bean, displayMax, limitTags);
    }

    protected final int applyProductDynamicItems(ProductBean bean, int displayMax, boolean limitTags) {
        int displayCount = 0;
        for (int displayItem : DYNAMIC_ITEMS) {
            boolean canShow = displayCount < displayMax;
            switch (displayItem) {
                case DYNAMIC_ITEM_BOGO: // BOGO - labelsView(iOS)
                    if (applyDynamicBogo(bean, canShow, limitTags)) {
                        displayCount++;
                    }
                    break;
                case DYNAMIC_ITEM_MKPL_SHIPS_FROM: // mkpl ships from - sold by (iOS)
                    if (applyDynamicMkplShipsFrom(bean, canShow, limitTags)) {
                        displayCount++;
                    }
                    break;
                case DYNAMIC_ITEM_MKPL_ETA_RANGE: // mkpl eta_range - eta (iOS)
                    if (applyDynamicMkplEtaRange(bean, canShow, limitTags)) {
                        displayCount++;
                    }
                    break;
                case DYNAMIC_ITEM_REMAINING: // remaining_tip - left (iOS)
                    if (applyDynamicRemaining(bean, canShow, limitTags)) {
                        displayCount++;
                    }
                    break;
                case DYNAMIC_ITEM_SOLD_NUM: // sold_num - sold count (iOS)
                    if (applyDynamicSoldNum(bean, canShow, limitTags)) {
                        displayCount++;
                    }
                    break;
                case DYNAMIC_ITEM_TOP_X: // top_x - entrance_tag (iOS)
                    if (applyDynamicTopX(bean, canShow, limitTags)) {
                        displayCount++;
                    }
                    break;
                case DYNAMIC_ITEM_PRE_SALE_DELIVERY_DESC:
                    if (applyDynamicPreSaleDeliveryDesc(bean, canShow, limitTags)) {
                        displayCount++;
                    }
                    break;
                default:
                    break;
            }
        }
        return displayCount;
    }

    protected boolean applyDynamicBogo(ProductBean bean, boolean canShow, boolean limitTags) {
        View layoutTags = helper.getView(R.id.layout_tags);
        boolean displayActivity = isVisibleItem(DYNAMIC_ITEM_BOGO)
                && !EmptyUtils.isEmpty(bean.product_tag_list)
                && canShow
                && layoutTags != null;
        if (displayActivity) {
            List<EntranceTag> tagList;
            tagList = limitTags ? CollectionUtils.subList(bean.product_tag_list, 0, 1) : bean.product_tag_list;
            if (layoutTags instanceof TagFlowLayout) {
                ProductViewHelper.setProductViewTag(getContext(), (TagFlowLayout) layoutTags, tagList, displayStyle);
            }
        }
        ViewTools.setViewVisibilityIfChanged(layoutTags, displayActivity);
        return displayActivity;
    }

    protected boolean applyDynamicMkplShipsFrom(ProductBean bean, boolean canShow, boolean limitTags) {
        TextView tvVendor = helper.getView(R.id.tv_vender);
        String vendorDeliveryDesc = bean.getVendorDeliveryNewDesc(bean.freeShippingDescShow(bean, limitTags, true));
        boolean deliveryInfoVisible = isShowMkplVendor && !EmptyUtils.isEmpty(vendorDeliveryDesc);
        boolean displayDeliveryInfo = isVisibleItem(DYNAMIC_ITEM_MKPL_SHIPS_FROM)
                && deliveryInfoVisible
                && canShow
                && tvVendor != null;
        if (displayDeliveryInfo) {
            tvVendor.setText(ViewTools.fromHtml(vendorDeliveryDesc, new DefaultURLClickListenerImpl<Void>()));
        }
        ViewTools.setViewVisibilityIfChanged(tvVendor, displayDeliveryInfo);
        return displayDeliveryInfo;
    }

    protected boolean applyDynamicMkplEtaRange(ProductBean bean, boolean canShow, boolean limitTags) {
        TextView tvEtaRange = helper.getView(R.id.tv_eta_range);
        boolean hasEtaRange = isShowMkplVendor && bean.isSeller() && bean.vender_info_view != null && !EmptyUtils.isEmpty(bean.vender_info_view.eta_range);
        boolean displayEtaRange = isVisibleItem(DYNAMIC_ITEM_MKPL_ETA_RANGE)
                && hasEtaRange
                && canShow
                && tvEtaRange != null;
        if (displayEtaRange) {
            tvEtaRange.setText(ViewTools.fromHtml(bean.vender_info_view.eta_range, new DefaultURLClickListenerImpl<Void>()));
        }
        ViewTools.setViewVisibilityIfChanged(tvEtaRange, displayEtaRange);
        return displayEtaRange;
    }

    protected boolean applyDynamicRemaining(ProductBean bean, boolean canShow, boolean limitTags) {
        TextView tvRemainingTip = helper.getView(R.id.tv_remaining_tip);
        boolean displayRemainingTip = isVisibleItem(DYNAMIC_ITEM_REMAINING)
                && OrderHelper.isShowRemainingTip(bean.remaining_count, bean.sold_count)
                && canShow
                && tvRemainingTip != null;
        if (displayRemainingTip) {
            tvRemainingTip.setText(getContext().getString(R.string.s_remaining_tip, bean.remaining_count));
        }
        ViewTools.setViewVisibilityIfChanged(tvRemainingTip, displayRemainingTip);
        return displayRemainingTip;
    }

    protected boolean applyDynamicSoldNum(ProductBean bean, boolean canShow, boolean limitTags) {
        TextView tvSoldNum = helper.getView(R.id.tv_sold_num);
        boolean displaySoldNum = isVisibleItem(DYNAMIC_ITEM_SOLD_NUM)
                && !EmptyUtils.isEmpty(bean.sold_count_ui)
                && canShow
                && tvSoldNum != null;
        if (displaySoldNum) {
            tvSoldNum.setText(bean.sold_count_ui);
        }
        ViewTools.setViewVisibilityIfChanged(tvSoldNum, displaySoldNum);
        return displaySoldNum;
    }

    protected boolean applyDynamicPreSaleDeliveryDesc(ProductBean bean, boolean canShow, boolean limitTags) {
        TextView tvDeliveryDesc = helper.getView(R.id.tv_delivery_desc);
        boolean hasDeliveryDesc = !EmptyUtils.isEmpty(bean.delivery_desc);
        boolean displayDeliveryDesc = isVisibleItem(DYNAMIC_ITEM_PRE_SALE_DELIVERY_DESC)
                && hasDeliveryDesc
                && canShow
                && tvDeliveryDesc != null;
        if (displayDeliveryDesc) {
            tvDeliveryDesc.setText(ViewTools.fromHtml(bean.delivery_desc, new DefaultURLClickListenerImpl<Void>()));
        }
        ViewTools.setViewVisibilityIfChanged(tvDeliveryDesc, displayDeliveryDesc);
        return displayDeliveryDesc;
    }

    protected SparseIntArray getVisibleItems() {
        return VISIBLE_ITEMS;
    }

    private boolean isVisibleItem(int itemType) {
        return getVisibleItems().indexOfKey(itemType) >= 0;
    }

    protected void toWebPage(String url) {
        Context context = getContext();
        if (context != null) {
            context.startActivity(WebViewActivity.getIntent(context, url));
        }
    }

    public ProductView setOnViewClickListener(int viewId, View.OnClickListener listener) {
        helper.setOnClickListener(viewId, listener);
        return this;
    }

    public void onCollectClick(View view, ProductBean bean) {
        if (AccountManager.get().isLogin()) {
            boolean result = CollectManager.get().toggleProductCollect(bean.id);
            ProductViewHelper.setProductCollect(helper.getView(R.id.iv_collect), result);
        } else {
            login();
        }
    }

    public void onProductClick(Context context, ProductBean product) {
        if (isSpecialProduct(product)) {
            context.startActivity(WebViewActivity.getIntent(context, product.view_link));
        } else {
            context.startActivity(ProductIntentCreator.getIntent(context, product));
        }
    }

    protected void onStatusClick(View view, ProductBean bean, int style) {
        onStatusClick(view, bean, style, null);
    }

    public void onStatusClick(View view, ProductBean bean, int style, OnStatusClickCallback onStatusClickCallback) {
        Context context = getContext();
        if (bean.isBundle() || bean.isHotDish()) {
            String url = bean.view_link;
            try {
                url = CommonTools.appendUri(bean.view_link, "trace_id=" + traceId);
            } catch (URISyntaxException ignored) {
                // no op
            }
            context.startActivity(WebViewActivity.getIntent(context, url));
        } else if (isChangeOtherDay(bean.sold_status)) {
            context.startActivity(DateActivity.getIntent(context, String.valueOf(bean.id), "product modify me"));
            if (onStatusClickCallback != null) {
                onStatusClickCallback.onStatusClick("product_change_date", EagleTrackEvent.ClickType.VIEW);
            }
        } else if (isSoldOut(bean.sold_status)) {
            if (AccountManager.get().isLogin()) {
                boolean isCollected = CollectManager.get().toggleProductCollect(bean.getProductId());
                displaySoldStatus(bean, style);
                if (isCollected && style == STYLE_CARD) {
                    TipsBarManager.get().notify(new TipsBean(getContext().getString(R.string.s_added_to_my_list), Constants.Url.COLLECT));
                }
                if (onStatusClickCallback != null) {
                    onStatusClickCallback.onStatusClick("product_notify_me", isCollected ? EagleTrackEvent.ClickType.NORMAL : "cancel_selection");
                }
            } else {
                context.startActivity(AccountIntentCreator.getIntent(context));
            }
        }
    }

    protected void applyProductBadge(ProductBean bean) {
        ShapeTextView tvMarker = helper.getView(R.id.tv_product_mark);
        if (tvMarker == null) {
            return;
        }
        Context context = tvMarker.getContext();
        int badgeRadius = CommonTools.dp2px(context, R.dimen.prop_badge_corner_size, 4f);
        int badgeStrokeWidth = CommonTools.dp2px(context, R.dimen.prop_size_border_200, 2f);
        boolean markerVisible = false;
        Integer topRankingScore = bean.getTopRankingScore();
        tvMarker.setCompoundDrawables(null, null, null, null);
        if (topRankingScore != null) {
            if (topRankingScore <= 3) {
                Drawable markerDrawable = ViewTools.getDrawableWithBounds(context, R.mipmap.ic_trophy_11x11, CommonTools.dp2px(11), CommonTools.dp2px(11));
                if (markerDrawable != null) {
                    tvMarker.setCompoundDrawables(markerDrawable, null, null, null);
                }
                tvMarker.setText(context.getString(R.string.s_top_x_before_3, topRankingScore));
                ViewTools.applyTextColor(tvMarker, R.color.brand_color_primary_4);
                ShapeHelper.setBackgroundDrawable(tvMarker, Color.parseColor("#F37519"), badgeRadius, Color.WHITE, badgeStrokeWidth);
            } else {
                tvMarker.setText(context.getString(R.string.s_top_x_after_3, topRankingScore));
                ViewTools.applyTextColor(tvMarker, R.color.color_surface_1_fg_default_idle);
                ShapeHelper.setBackgroundDrawable(tvMarker, Color.parseColor("#F9E2B4"), badgeRadius, Color.WHITE, badgeStrokeWidth);
            }
            markerVisible = true;
        } else if (isPreSell(bean.sold_status)) {
            tvMarker.setText(R.string.s_pre_sell);
            tvMarker.setTextColor(Color.WHITE);
            ShapeHelper.setBackgroundDrawable(tvMarker, Color.parseColor("#820B6A"), badgeRadius, Color.WHITE, badgeStrokeWidth);
            markerVisible = true;
        } else {
            ProductBean.LabelListBean label = CollectionUtils.firstOrNull(bean.label_list);
            if (label != null) {
                try {
                    tvMarker.setText(label.label_name);
                    tvMarker.setTextColor(ViewTools.parseColor(label.label_font_color, Color.WHITE));
                    ShapeHelper.setBackgroundDrawable(tvMarker, Color.parseColor(label.label_color), badgeRadius, Color.WHITE, badgeStrokeWidth);
                    markerVisible = true;
                } catch (Exception ignored) {
                    // do nothing
                }
            }
        }
        ViewTools.setViewVisibilityIfChanged(tvMarker, markerVisible);
    }

    protected void displaySoldStatus(ProductBean bean, int style) {
        CartStatusLayout layoutStatus = helper.getView(R.id.layout_status);
        boolean isCollected = CollectManager.get().isProductCollect(bean.id);
        boolean hasRestockTip = !EmptyUtils.isEmpty(bean.restock_tip);
        if (style == STYLE_LIST && layoutStatus != null) {
            if (isCollected) {
                layoutStatus.showTooltip(R.string.s_added_to_my_list, R.mipmap.iccmpt_carat_right_20x20);
                layoutStatus.setOnTooltipClickListener(new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        if (CollectManager.get().isProductCollect(bean.getProductId())) {
                            toWebPage(Constants.Url.COLLECT);
                        }
                    }
                });
            } else {
                if (hasRestockTip) {
                    layoutStatus.showTooltip(bean.restock_tip, null);
                } else {
                    layoutStatus.hideTooltip();
                }
                layoutStatus.setOnTooltipClickListener(null);
            }
        }

        if (isCollected) {
            ProductViewHelper.setProductStatusNotificationSet(layoutStatus);
        } else {
            ProductViewHelper.setProductStatusNotifyMe(layoutStatus);
        }
    }

    protected boolean applyDynamicTopX(ProductBean bean, boolean canShow, boolean limitTags) {
        Context context = getContext();
        boolean displayTopX = isVisibleItem(DYNAMIC_ITEM_TOP_X)
                && canShow
                && displayStyle != STYLE_ITEM_SEARCH_V2
                && bean.entrance_tag != null && !EmptyUtils.isEmpty(bean.entrance_tag.tag_name);
        View topX = helper.getView(R.id.layout_top_x);
        ViewTools.setViewVisibilityIfChanged(topX, displayTopX);
        ViewTools.setViewVisibilityIfChanged(helper.getView(R.id.layout_top_x_parent), displayTopX);
        if (!displayTopX) {
            return false;
        }

        int badgeRadius = CommonTools.dp2px(context, R.dimen.prop_badge_corner_size, 4f);
        int tagColorInt = ViewTools.parseColor(context, bean.entrance_tag.tag_color, R.color.root_color_orange_spectrum_2);
        Drawable tagColorDrawable = new ColorDrawable(tagColorInt);
        boolean displayHeadIcon = !EmptyUtils.isEmpty(bean.entrance_tag.tag_icon_url);
        helper.setVisible(R.id.iv_top_x_icon, displayHeadIcon);
        if (displayHeadIcon) {
            ImageLoader.load(context, helper.getView(R.id.iv_top_x_icon), bean.entrance_tag.tag_icon_url, tagColorDrawable);
        }
        boolean displayTailIcon = !EmptyUtils.isEmpty(bean.entrance_tag.tail_icon_url);
        helper.setVisible(R.id.iv_top_x_tail_icon, displayTailIcon);
        if (displayTailIcon) {
            ImageLoader.load(context, helper.getView(R.id.iv_top_x_tail_icon), WebpManager.get().getConvertUrl(ImageSpec.SPEC_16, bean.entrance_tag.tail_icon_url), tagColorDrawable);
        }

        String tagName = bean.entrance_tag.tag_name;
        Label label = new Label(tagName, null, bean.entrance_tag.tag_font_color, bean.entrance_tag.tag_color, null);
        LabelEntity labelEntity = new LabelEntityBuilder(label)
                .textAppearanceResId(R.style.style_fluid_root_badge_label_sm)
                .paddingHorizontalResId(R.dimen.sw_4dp)
                .paddingVerticalResId(R.dimen.sw_0dp)
                .build();

        TagFlowLabelLayout tagView = helper.getView(R.id.tfl_label);
        tagView.setTagFlowAdapter(ProductViewHelper.getTagFlowLabelAdapter(CollectionUtils.arrayListOf(labelEntity)));
        ShapeHelper.setBackgroundSolidDrawable(topX, tagColorInt, badgeRadius);
        helper.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                Context ctx = v.getContext();
                if (ctx != null && !EmptyUtils.isEmpty(bean.entrance_tag.more_link)) {
                    ctx.startActivity(WebViewActivity.getIntent(ctx, bean.entrance_tag.more_link));
                }
            }
        }, R.id.v_top_x_click_area);
        return true;
    }

    protected Integer getProductNameMaxLines() {
        return LanguageManager.get().isCJK() ? 2 : 3;
    }

    protected static boolean isSpecialProduct(ProductBean product) {
        return product != null &&
                (Constants.ProductType.BUNDLE.equals(product.category)
                        || Constants.ProductType.VALUE_HOT_DISH.equals(product.is_hotdish)
                        || Constants.ProductStatus.PRE_SELL.equalsIgnoreCase(product.sold_status)
                );
    }

    protected void login() {
        Context context = getContext();
        if (context != null) {
            context.startActivity(AccountIntentCreator.getIntent(context));
        }
    }

    public static boolean isSoldOut(String status) {
        return Constants.ProductStatus.SOLD_OUT.equalsIgnoreCase(status);
    }

    public static boolean isChangeOtherDay(String status) {
        return Constants.ProductStatus.CHANGE_OTHER_DAY.equalsIgnoreCase(status);
    }

    public static boolean isReachLimit(String status) {
        return Constants.ProductStatus.REACH_LIMIT.equalsIgnoreCase(status);
    }

    public static boolean isPreSell(String status) {
        return Constants.ProductStatus.PRE_SELL.equalsIgnoreCase(status);
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    private void applyFactorsIfSearchV2(int style) {
        float factor = style == STYLE_ITEM_SEARCH_V2 ? Constants.SearchV2.PRODUCT_CARD_SIZE_FACTOR_TO_SHOW_MORE_PRODUCTS : 1f;
        productNameLabelBadgeScaleFactor = factor;
        if (factor == 1) {
            return;
        }
        float factorMid = (1f + factor) / 2f;

        applyTextViewCustomScaleFactor(R.id.tv_sold_out, factor);
        applyTextViewCustomScaleFactor(R.id.tv_status, factor);
        applyTextViewCustomScaleFactor(R.id.tv_sold_num, factor);
        applyTextViewCustomScaleFactor(R.id.tv_num, factor);
        applyTextViewCustomScaleFactor(R.id.tv_edit_num, factorMid);
        applyTextViewCustomScaleFactor(R.id.tv_price, factorMid);
        applyTextViewCustomScaleFactor(R.id.tv_price_delete, factorMid);
        applyTextViewCustomScaleFactor(R.id.tv_brand_name, factorMid);
        applyTextViewCustomScaleFactor(R.id.tv_delivery_desc, factorMid);
        applyTextViewCustomScaleFactor(R.id.tv_remaining_tip, factorMid);
        applyTextViewCustomScaleFactor(R.id.tv_eta_range, factorMid);
        applyTextViewCustomScaleFactor(R.id.tv_vender, factorMid);
        applyTextViewCustomScaleFactor(R.id.tv_price_volume, factorMid);
        applyTextViewCustomScaleFactor(R.id.tv_price_single, factorMid);

        ViewGroup llStatus = helper.getView(R.id.ll_status);
        ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) llStatus.getLayoutParams();
        lp.height = (int) (lp.height * factorMid);
        llStatus.setLayoutParams(lp);
        ViewTools.updatePaddings(
                llStatus,
                (int) (llStatus.getPaddingLeft() * factorMid),
                (int) (llStatus.getPaddingTop() * factorMid),
                (int) (llStatus.getPaddingRight() * factorMid),
                (int) (llStatus.getPaddingBottom() * factorMid)
        );

        TextView tvSoldOut = helper.getView(R.id.tv_sold_out);
        tvSoldOut.setIncludeFontPadding(false);
        ViewTools.updatePaddings(
                tvSoldOut,
                tvSoldOut.getPaddingLeft(),
                tvSoldOut.getPaddingTop(),
                tvSoldOut.getPaddingRight(),
                lp.height + lp.bottomMargin
        );

        CartOpLayout layoutOp = getCartOpLayout();
        if (layoutOp != null) {
            layoutOp.setTransValue((int) (layoutOp.getTransValue() * factor));

            ConstraintLayout layoutOpView = layoutOp.findViewById(R.id.layout_op_view);
            if (layoutOpView != null) {
                ConstraintLayout.LayoutParams clp = (ConstraintLayout.LayoutParams) layoutOpView.getLayoutParams();
                clp.height = (int) (getContext().getResources().getDimensionPixelOffset(R.dimen.prop_size_atc_mini) * factor);
                layoutOpView.setLayoutParams(clp);

                TextView tvTips = layoutOp.findViewById(R.id.tv_tips);
                if (tvTips != null) {
                    layoutOp.setClipChildren(false);
                    ConstraintLayout layoutProduct = helper.getView(R.id.layout_product);
                    if (layoutProduct != null) layoutProduct.setClipChildren(false);

                    layoutOp.setTipsTextSizeFactor(factor);
                    TextAppearanceSpan normal = layoutOp.getTipsNormalTextAppearanceSpan();
                    layoutOp.setTipsNormalTextAppearanceSpan(new TextAppearanceSpan(
                            normal.getFamily(),
                            normal.getTextStyle(),
                            (int) (normal.getTextSize() * factor),
                            normal.getTextColor(),
                            normal.getLinkTextColor()
                    ));
                    TextAppearanceSpan bold = layoutOp.getTipsBoldTextAppearanceSpan();
                    layoutOp.setTipsBoldTextAppearanceSpan(new TextAppearanceSpan(
                            bold.getFamily(),
                            bold.getTextStyle(),
                            (int) (bold.getTextSize() * factor),
                            bold.getTextColor(),
                            bold.getLinkTextColor()
                    ));
                }
            }
        }
    }

    private void applyTextViewCustomScaleFactor(int textViewId, float factor) {
        View v = helper.getView(textViewId);
        if (v instanceof TextView) {
            TextView textView = (TextView) v;
            textView.setMinimumHeight((int) (textView.getMinimumHeight() * factor));
            textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, textView.getTextSize() * factor);
            if (textViewId == R.id.tv_sold_num) {
                ViewTools.updatePaddings(
                        textView,
                        (int) (textView.getPaddingLeft() * factor),
                        (int) (textView.getPaddingTop() * factor),
                        (int) (textView.getPaddingRight() * factor),
                        (int) (textView.getPaddingBottom() * factor)
                );
            }
        }
    }

    public interface OnOpCallback {
        void onOp(CartOpLayout layoutOp, ProductBean bean);
    }

    public interface OnCollectClickCallback {
        void onCollectClick();
    }

    public interface OnPdpClickCallback {
        void onPdpClick();
    }

    public interface OnStatusClickCallback {
        void onStatusClick(String targetType, String clickType);
    }
}