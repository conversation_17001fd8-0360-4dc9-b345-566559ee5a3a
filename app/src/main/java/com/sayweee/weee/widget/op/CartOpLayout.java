package com.sayweee.weee.widget.op;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Typeface;
import android.text.style.StyleSpan;
import android.text.style.TextAppearanceSpan;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.IntDef;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewTreeLifecycleOwner;

import com.sayweee.core.order.SharedOrderViewModel;
import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.service.VibratorManager;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.TalkBackHelper;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.wrapper.utils.Spanny;

/**
 * Author:  winds
 * Date:    2021/9/8.
 * Desc:
 */
public class CartOpLayout extends LinearLayout implements Observer<Integer> {

    public static final int OP_STATUS_NORMAL = 0;   //正常状态 显示蓝色背景+号
    public static final int OP_STATUS_COLLAPSE = 1; //收缩状态 显示蓝色背景购物车数量
    public static final int OP_STATUS_EXPAND = 2;   //展开状态
    public static final int OP_STATUS_DISABLE = 3;  //禁用状态 显示灰色背景+号
    public static final int OP_STATUS_OTHER = 4;    //其他

    public static final int STYLE_STANDARD = 0;  // 标准 40x40
    public static final int STYLE_SMALL = 1;     // 小号 24x24

    public static final int BUBBLE_STYLE_DARK = 0;  // 暗色气泡
    public static final int BUBBLE_STYLE_LIGHT = 1; // 亮色气泡

    @IntDef({OP_STATUS_NORMAL, OP_STATUS_COLLAPSE, OP_STATUS_EXPAND, OP_STATUS_DISABLE, OP_STATUS_OTHER})
    private @interface OpStatus {

    }

    protected int opStatus;
    protected OnCartOpListener listener;
    protected int transValue = CommonTools.dp2px(60);

    protected View layoutOpGroup;
    protected OpViewStyle opViewStyle;

    @SuppressWarnings("UnusedReturnValue")
    public CartOpLayout setOpStatus(@OpStatus int opStatus) {
        this.opStatus = opStatus;
        updateTalkback();
        return this;
    }

    public @OpStatus int getOpStatus() {
        return this.opStatus;
    }

    protected int style = STYLE_STANDARD;
    protected ViewGroup layoutOp;
    protected OpView ivEditLeft, ivEditRight;
    protected TextView tvEditNum, tvNum, tvTips;
    protected View layoutTips;

    protected static final long DEFAULT_ANIMATION_DURATION = 150L;   //动画时间
    protected static final long SHOW_TIPS_DURATION = 3000L;

    protected TextAppearanceSpan tipsNormalTextAppearanceSpan;
    protected TextAppearanceSpan tipsBoldTextAppearanceSpan;

    protected long animateDuration = DEFAULT_ANIMATION_DURATION;
    protected AnimatorSet expandSet;
    protected AnimatorSet collapseSet;
    private final Runnable tipsRunnable = this::dismissTips;

    protected final Observer<Integer> errorObserver = new Observer<Integer>() {
        @Override
        public void onChanged(Integer integer) {
            if (integer != null && listener != null && listener.getProductId() == integer) {
                int itemNum = OrderManager.get().getCartItemNum(integer);
                if (itemNum == 0) {
                    setTextNum(0, opStatus == OP_STATUS_EXPAND);
                }
            }
        }
    };

    public CartOpLayout(Context context) {
        this(context, null);
    }

    public CartOpLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CartOpLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        LifecycleOwner lifecycleOwner = ViewTreeLifecycleOwner.get(this);
        if (lifecycleOwner != null) {
            SharedOrderViewModel sharedViewModel;
            try {
                sharedViewModel = SharedOrderViewModel.get();
            } catch (Exception ignore) {
                sharedViewModel = null;
            }
            if (sharedViewModel != null) {
                sharedViewModel.productOpStatusData.observe(lifecycleOwner, this);
                sharedViewModel.productOpErrorData.observe(lifecycleOwner, errorObserver);
            }
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        if (layoutTips != null) {
            layoutTips.removeCallbacks(this.tipsRunnable);
        }
        SharedOrderViewModel sharedViewModel;
        try {
            sharedViewModel = SharedOrderViewModel.get();
        } catch (Exception ignore) {
            sharedViewModel = null;
        }
        if (sharedViewModel != null) {
            sharedViewModel.productOpStatusData.removeObserver(this);
            sharedViewModel.productOpErrorData.removeObserver(errorObserver);
        }
        super.onDetachedFromWindow();
    }

    @Override
    public void onChanged(Integer integer) {
        if (listener != null && integer != null && opStatus == OP_STATUS_EXPAND) {
            int productId = listener.getProductId();
            if (productId != integer) {
                collapseWithAnim();
            }
            if (changeListener != null) {
                changeListener.onChange(integer);
            }
        }
    }


    public void vibrate() {
        VibratorManager.vibrate();
    }

    @SuppressWarnings("UnusedReturnValue")
    public CartOpLayout setLeftRemoveStyle() {
        ivEditLeft.setRemoveStyle();
        ivEditLeft.setEnabled(true);
        return this;
    }

    @SuppressWarnings("UnusedReturnValue")
    public CartOpLayout setLeftMinusStyle() {
        ivEditLeft.setMinusStyle();
        ivEditLeft.setEnabled(true);
        return this;
    }

    @SuppressWarnings("UnusedReturnValue")
    public CartOpLayout setRightAddStyle() {
        ivEditRight.setAddStyle();
        ivEditRight.setEnabled(true);
        return this;
    }

    @SuppressWarnings("UnusedReturnValue")
    public CartOpLayout setRightFullStyle() {
        ivEditRight.setFullStyle();
        ivEditRight.setEnabled(true);
        return this;
    }

    private CartOpLayout setRightDisableStyle() {
        ivEditRight.setFullStyle();
        ivEditRight.setEnabled(false);
        return this;
    }

    @SuppressWarnings("UnusedReturnValue")
    public CartOpLayout setDisableStyle() {
        collapse();
        tvNum.setVisibility(GONE);
        ivEditRight.setVisibility(VISIBLE);
        return setRightDisableStyle();
    }

    protected int getLayoutRes(int style) {
        int layoutRes;
        if (style == STYLE_SMALL) {
            transValue = CommonTools.dp2px(42);
            layoutRes = R.layout.layout_cart_op_enki_2_small;
        } else { // STYLE_STANDARD
            transValue = CommonTools.dp2px(60);
            layoutRes = R.layout.layout_cart_op_enki_2;
        }
        return layoutRes;
    }

    @SuppressWarnings("resource")
    protected void init(Context context, AttributeSet attrs) {
        style = STYLE_STANDARD;
        int opMarginEnd = 0;
        int opMarginBottom = 0;
        int bubbleStyle = BUBBLE_STYLE_DARK;
        if (attrs != null) {
            TypedArray ta = context.getTheme().obtainStyledAttributes(attrs, R.styleable.CartOpLayout, 0, 0);
            style = ta.getInt(R.styleable.CartOpLayout_op_style, STYLE_STANDARD);
            if (ta.hasValue(R.styleable.CartOpLayout_op_bubble_style)) {
                bubbleStyle = ta.getInt(R.styleable.CartOpLayout_op_bubble_style, BUBBLE_STYLE_DARK);
            }
            if (ta.hasValue(R.styleable.CartOpLayout_op_marginEnd)) {
                opMarginEnd = ta.getDimensionPixelOffset(R.styleable.CartOpLayout_op_marginEnd, 0);
            }
            if (ta.hasValue(R.styleable.CartOpLayout_op_marginBottom)) {
                opMarginBottom = ta.getDimensionPixelOffset(R.styleable.BottomOpLayout_bottom_op_marginBottom, 0);
            }
            ta.recycle();
        }

        int layoutRes = getLayoutRes(style);
        View view = inflate(getContext(), layoutRes, this);
        layoutOp = view.findViewById(R.id.layout_op_view);
        layoutOpGroup = findViewById(R.id.layout_op_shadow);
        ivEditLeft = view.findViewById(R.id.iv_edit_left);
        ivEditRight = view.findViewById(R.id.iv_edit_right);
        tvEditNum = view.findViewById(R.id.tv_edit_num);
        tvNum = view.findViewById(R.id.tv_num);
        tvTips = view.findViewById(R.id.tv_tips);
        layoutTips = view.findViewById(R.id.layout_tips);

        ivEditLeft.setDelegate(opViewStyle);
        ivEditRight.setDelegate(opViewStyle);

        setRightAddStyle();
        updateBubbleStyle(bubbleStyle);
        ViewTools.updateMargins(layoutOp, null, null, opMarginEnd, opMarginBottom);

        ivEditLeft.setOnClickListener(v -> {
            vibrate();
            if (listener != null) {
                listener.operateLeft(v);
            }
        });

        ivEditRight.setOnClickListener(v -> {
            vibrate();
            if (listener != null) {
                listener.operateRight(v);
            }
        });

        tvNum.setOnClickListener(new OnSafeClickListener() {
            @Override
            public void onClickSafely(View v) {
                vibrate();
                if (listener != null) {
                    listener.onNumClick(v);
                }
            }
        });

        // for accessibility
        tvEditNum.setOnClickListener(v -> {
        });
    }

    protected void updateBubbleStyle(int bubbleStyle) {
        BubbleLayout bubble = null;
        if (layoutTips instanceof BubbleLayout) {
            bubble = (BubbleLayout) layoutTips;
        }
        if (bubble == null) {
            return;
        }

        if (bubbleStyle == BUBBLE_STYLE_LIGHT) {
            bubble.setBubbleColor(ContextCompat.getColor(getContext(), R.color.root_color_white_static));
            tvTips.setTextColor(ContextCompat.getColor(getContext(), R.color.text_main));
        } else { // BUBBLE_STYLE_DARK
            bubble.setBubbleColor(ContextCompat.getColor(getContext(), R.color.color_surface_500_bg));
            tvTips.setTextColor(ContextCompat.getColor(getContext(), R.color.color_reserved_true_white));
        }
    }

    /**
     * 展示数量上限的提示
     */
    public void showReachedTips() {
        showOpTips(getContext().getString(R.string.s_qty_limit_reached));
    }

    public void showMinPurchaseTips(int num) {
        showOpTips(String.format(getResources().getString(R.string.s_min_purchase_tips), num));
    }

    public void showVolumePriceTips(double diff, int volumeThreshold) {
        Spanny tips = new Spanny();
        Context context = tvTips.getContext();
        if (context != null) {
            String s = OrderHelper.formatUSMoney(diff);
            boolean isChinese = LanguageManager.get().isSimpleChinese() || LanguageManager.get().isTraditionalChinese();
            if (isChinese) {
                tips.append(String.format(getResources().getString(R.string.s_when_you_buy), volumeThreshold), getTipsNormalTextAppearanceSpan());
                tips.append("\n");
                tips.append(String.format(getResources().getString(R.string.s_save_ea), s), new StyleSpan(Typeface.BOLD), getTipsBoldTextAppearanceSpan());
            } else {
                tips.append(String.format(getResources().getString(R.string.s_save_ea), s), new StyleSpan(Typeface.BOLD), getTipsBoldTextAppearanceSpan());
                tips.append("\n");
                tips.append(String.format(getResources().getString(R.string.s_when_you_buy), volumeThreshold), getTipsNormalTextAppearanceSpan());
            }
            showOpTips(tips);
        }
    }

    public void showOpTips(CharSequence charSequence) {
        dismissTips();
        tvTips.setText(charSequence);
        layoutTips.setVisibility(VISIBLE);
        layoutTips.postDelayed(this.tipsRunnable, SHOW_TIPS_DURATION);
    }

    public void onlyShowOpTips() {
        tvTips.setText(getContext().getString(R.string.s_qty_limit_reached));
        layoutTips.setVisibility(VISIBLE);
        layoutTips.postDelayed(this.tipsRunnable, SHOW_TIPS_DURATION);
    }

    public void onlyShowMinPurchaseTips(int num) {
        tvTips.setText(String.format(getResources().getString(R.string.s_min_purchase_tips), num));
        layoutTips.setVisibility(VISIBLE);
        layoutTips.postDelayed(this.tipsRunnable, SHOW_TIPS_DURATION);
    }

    public void dismissTips() {
        layoutTips.removeCallbacks(tipsRunnable);
        layoutTips.setVisibility(GONE);
    }

    public void setOpStyle(int num, int min, int max) {
        if (num >= 0 && num <= min) {
            setLeftRemoveStyle();
        } else {
            setLeftMinusStyle();
        }
        if (num > 0 && num >= max) {
            setRightFullStyle();
        } else {
            setRightAddStyle();
        }
    }

    public CartOpLayout expandWithAnim() {
        if (collapseSet != null && collapseSet.isRunning()) {
            collapseSet.cancel();
        }
        if (expandSet != null && expandSet.isRunning()) {
            return this;
        }
        ViewGroup.MarginLayoutParams params = (MarginLayoutParams) ivEditLeft.getLayoutParams();
        if (params != null && params.rightMargin < transValue) {
            ValueAnimator expandAnimator = ValueAnimator.ofInt(params.rightMargin, transValue);
            expandAnimator.addUpdateListener(animation -> {
                Object value = animation.getAnimatedValue();
                if (value != null) {
                    params.rightMargin = (Integer) value;
                    ivEditLeft.setLayoutParams(params);
                }
            });

            expandSet = new AnimatorSet();
            expandSet.setDuration(animateDuration).play(expandAnimator).with(ObjectAnimator.ofFloat(ivEditLeft, "rotation", Math.min(ivEditLeft.getRotation(), 90), 0));
            expandSet.addListener(new AnimatorListenerAdapter() {

                @Override
                public void onAnimationStart(Animator animation) {
                    super.onAnimationStart(animation);
                    ivEditLeft.setVisibility(VISIBLE);
                    ivEditRight.setVisibility(VISIBLE);
                    tvEditNum.setVisibility(VISIBLE);
                    tvNum.setVisibility(GONE);
                }

                @Override
                public void onAnimationCancel(Animator animation) {
                    expand();
                }
            });
            expandSet.start();
        }
        setOpStatus(OP_STATUS_EXPAND);
        return this;
    }

    public CartOpLayout collapseWithAnim() {
        if (expandSet != null && expandSet.isRunning()) {
            expandSet.cancel();
        }
        if (collapseSet != null && collapseSet.isRunning()) {
            return this;
        }
        ViewGroup.MarginLayoutParams params = (MarginLayoutParams) ivEditLeft.getLayoutParams();
        if (params != null && params.rightMargin > 0) {
            ValueAnimator collapseAnimator = ValueAnimator.ofInt(params.rightMargin, 0);
            collapseAnimator.addUpdateListener(animation -> {
                Object value = animation.getAnimatedValue();
                if (value != null) {
                    params.rightMargin = (Integer) value;
                    ivEditLeft.setLayoutParams(params);
                }
            });

            collapseSet = new AnimatorSet();
            collapseSet.setDuration(animateDuration).play(collapseAnimator).with(ObjectAnimator.ofFloat(ivEditLeft, "rotation", ivEditLeft.getRotation(), 90));
            collapseSet.addListener(new AnimatorListenerAdapter() {

                @Override
                public void onAnimationEnd(Animator animation) {
                    super.onAnimationEnd(animation);
                    ivEditLeft.setVisibility(GONE);
                    tvEditNum.setVisibility(GONE);
                    int num = DecimalTools.parseInt(tvEditNum.getText().toString());
                    ivEditRight.setVisibility(num > 0 ? GONE : VISIBLE);
                    tvNum.setVisibility(num > 0 ? VISIBLE : GONE);
                    dismissTips();
                }

                @Override
                public void onAnimationCancel(Animator animation) {
                    collapse();
                }
            });
            collapseSet.start();
        }
        setOpStatus(OP_STATUS_COLLAPSE);
        return this;
    }

    public CartOpLayout expand() {
        ivEditLeft.setVisibility(VISIBLE);
        ivEditRight.setVisibility(VISIBLE);
        tvEditNum.setVisibility(VISIBLE);
        tvNum.setVisibility(GONE);

        ViewGroup.MarginLayoutParams params = (MarginLayoutParams) ivEditLeft.getLayoutParams();
        if (params != null) {
            params.rightMargin = transValue;
            ivEditLeft.setLayoutParams(params);
        }
        setOpStatus(OP_STATUS_EXPAND);
        return this;
    }

    public CartOpLayout collapse() {
        ivEditLeft.setVisibility(GONE);
        tvEditNum.setVisibility(GONE);
        int num = DecimalTools.parseInt(tvEditNum.getText().toString());
        ivEditRight.setVisibility(num > 0 ? GONE : VISIBLE);
        tvNum.setVisibility(num > 0 ? VISIBLE : GONE);

        ViewGroup.MarginLayoutParams params = (MarginLayoutParams) ivEditLeft.getLayoutParams();
        if (params != null) {
            params.rightMargin = 0;
            ivEditLeft.setLayoutParams(params);
        }
        dismissTips();
        setOpStatus(OP_STATUS_COLLAPSE);
        updateTalkback();
        return this;
    }

    public int getTextNum() {
        CharSequence cs = tvNum.getText();
        if (cs == null || cs.length() == 0) {
            return 0;
        }
        return Integer.parseInt(cs.toString());
    }

    @SuppressWarnings("unused")
    public CartOpLayout setTextNum(int num) {
        return setTextNum(num, false);
    }

    @SuppressWarnings("UnusedReturnValue")
    public CartOpLayout setTextNumAnim(int num) {
        return setTextNum(num, true);
    }

    public CartOpLayout setTextNum(int num, boolean anim) {
        if (num <= 0) {
            dismissTips();
        }
        updateNum(num);
        if (anim) {
            return num > 0 ? expandWithAnim() : collapseWithAnim();
        } else {
            return num > 0 ? expand() : collapse();
        }
    }

    @SuppressWarnings("UnusedReturnValue")
    public CartOpLayout setOpNum(int num) {
        dismissTips();
        setOpNumDirect(num);
        return collapse();
    }

    @SuppressWarnings("UnusedReturnValue")
    public CartOpLayout setOpNumDirect(int num) {
        dismissTips();
        updateNum(num);
        return this;
    }

    protected void updateNum(int num) {
        String numText = num > 0 ? String.valueOf(num) : "0";
        tvNum.setText(numText);
        tvEditNum.setText(numText);
        updateTalkback();
    }

    protected void updateTalkback() {
        String numText = tvNum.getText() != null ? tvNum.getText().toString() : "0";
        boolean hasItems = !"0".equals(numText);
        boolean isCollapsed = getOpStatus() != OP_STATUS_EXPAND;
        if (isCollapsed) {
            // +
            // num
            String contentDescAdd = hasItems
                    ? getContext().getString(R.string.a_cart_op_expand_with_num, numText)
                    : getContext().getString(R.string.a_cart_op_expand);
            TalkBackHelper.setContentDesc(tvEditNum, getContext().getString(R.string.a_cart_op_num, numText));
            TalkBackHelper.setContentDesc(ivEditRight, contentDescAdd);
            TalkBackHelper.setContentDescId(ivEditLeft, R.string.a_cart_op_remove_item);
            TalkBackHelper.setContentDesc(tvNum, contentDescAdd);
        } else {
            // - editNum +
            TalkBackHelper.setContentDesc(tvEditNum, getContext().getString(R.string.a_cart_op_num, numText));
            TalkBackHelper.setContentDescId(ivEditRight, R.string.a_cart_op_add_item);
            TalkBackHelper.setContentDescId(ivEditLeft, R.string.a_cart_op_remove_item);
            TalkBackHelper.setContentDesc(tvNum, getContext().getString(R.string.a_cart_op_num, numText));
        }
    }

    public int getTransValue() {
        return transValue;
    }

    public void setTransValue(int transValue) {
        this.transValue = transValue;
    }

    public TextAppearanceSpan getTipsNormalTextAppearanceSpan() {
        if (tipsNormalTextAppearanceSpan == null) {
            tipsNormalTextAppearanceSpan = new TextAppearanceSpan(tvTips.getContext(), R.style.style_fluid_root_utility_xs_subdued);
        }
        return tipsNormalTextAppearanceSpan;
    }

    public TextAppearanceSpan getTipsBoldTextAppearanceSpan() {
        if (tipsBoldTextAppearanceSpan == null) {
            tipsBoldTextAppearanceSpan = new TextAppearanceSpan(tvTips.getContext(), R.style.style_fluid_root_utility_xs_bold);
        }
        return tipsBoldTextAppearanceSpan;
    }

    public void setTipsTextSizeFactor(float sizeFactor) {
        this.tvTips.setTextSize(TypedValue.COMPLEX_UNIT_PX, this.tvTips.getTextSize() * sizeFactor);
    }

    public void setTipsNormalTextAppearanceSpan(TextAppearanceSpan tipsNormalTextAppearanceSpan) {
        this.tipsNormalTextAppearanceSpan = tipsNormalTextAppearanceSpan;
    }

    public void setTipsBoldTextAppearanceSpan(TextAppearanceSpan tipsBoldTextAppearanceSpan) {
        this.tipsBoldTextAppearanceSpan = tipsBoldTextAppearanceSpan;
    }

    public void setOnOperateListener(OnCartOpListener listener) {
        this.listener = listener;
    }

    public OnCartOpListener getListener() {
        return listener;
    }

    public void setTipsMaxWidth(int maxWidth) {
        if (this.tvTips != null && this.tvTips.getMaxWidth() != maxWidth) {
            this.tvTips.setMaxWidth(maxWidth);
        }
    }


    private OnChangeListener changeListener;

    public void setOnChangeListener(OnChangeListener listener) {
        this.changeListener = listener;
    }

    public interface OnChangeListener {
        void onChange(int num);
    }

    public interface OnCartOpListener {

        void operateLeft(View view);

        void operateRight(View view);

        void onNumClick(View view);

        int getProductId();
    }

}
