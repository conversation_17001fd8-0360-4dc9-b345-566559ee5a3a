package com.sayweee.weee.service.analytics;

import com.sayweee.weee.global.manager.AppTracker;
import com.sayweee.weee.module.home.bean.ImpressionBean;
import com.sayweee.weee.service.track.ImpressionTracker;

public class ImpressionTrackerIml extends ImpressionTracker<ImpressionBean> {

    @Override
    public void trackImpression(ImpressionBean event) {
        if (!isTracked(event.getKey())) {
            setTracked(event.getKey());
            AppTracker.get().track(event.eventName, event.params);
        }
    }
}
