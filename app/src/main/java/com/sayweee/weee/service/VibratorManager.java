package com.sayweee.weee.service;

import android.os.Vibrator;

import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

import static android.content.Context.VIBRATOR_SERVICE;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/12/4.
 * Desc:
 */
public class VibratorManager {

    static Vibrator vibrator;

    public static void vibrate() {
        if (vibrator == null) {
            vibrator = (Vibrator) LifecycleProvider.get().getApplication().getSystemService(VIBRATOR_SERVICE);
        }
        if (vibrator != null) {
            vibrator.vibrate(20);
        }
    }

}
