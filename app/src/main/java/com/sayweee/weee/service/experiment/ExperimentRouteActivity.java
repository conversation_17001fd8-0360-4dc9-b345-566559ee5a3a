package com.sayweee.weee.service.experiment;

import android.app.ActivityOptions;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;

import com.sayweee.service.ConfigService;
import com.sayweee.service.config.exp.bean.ExperimentBean;
import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.StatusBarManager;
import com.sayweee.weee.module.web.WebRouter;
import com.sayweee.weee.module.web.WebViewModel;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.wrapper.core.view.WrapperMvvmActivity;

//
// Created by <PERSON><PERSON> on 8/1/24.
// Copyright (c) 2024 Weee LLC. All rights reserved.
//
public class ExperimentRouteActivity extends WrapperMvvmActivity<WebViewModel> {

    private static final String BUNDLE_EXPERIMENT_ID = "bundle_experiment_id";
    private static final String BUNDLE_URL = "bundle_url";

    private static final String EXPERIMENT_NOT_IN = "2";

    public static Intent getIntent(Context context, int experimentId, String url) {
        return new Intent(context, ExperimentRouteActivity.class)
                .putExtra(BUNDLE_EXPERIMENT_ID, experimentId)
                .putExtra(BUNDLE_URL, url);
    }

    @Override
    public boolean useWrapper() {
        return false;
    }

    @Override
    public int getLayoutRes() {
        return R.layout.activity_experiment_route;
    }

    @Override
    protected void initStatusBar() {

    }

    @Override
    public void initView(View view, Bundle savedInstanceState) {

    }

    @Override
    public void attachModel() {

    }

    @Override
    public void loadData() {
        final int experimentId = getIntent().getIntExtra(BUNDLE_EXPERIMENT_ID, 0);
        final String url = getIntent().getStringExtra(BUNDLE_URL);
        if (experimentId == 0 || EmptyUtils.isEmpty(url)) {
            finish();
            return;
        }
        ConfigService.get().fetchExperiment(false, experimentId, ((result, failureBean) -> {
            if (!ConfigService.get().isExperimentRequested(experimentId)) {
                // default not in experiment
                ExperimentBean experiment = ExperimentBean.ofControl(experimentId);
                ConfigService.get().setExperiment(experimentId, experiment);
            }
            gotoUrl(url);
        }));
    }

    private void gotoUrl(@NonNull String url) {
        Bundle options = ActivityOptions.makeCustomAnimation(this, 0, 0).toBundle();
        WebRouter.toPageFinish(this, url, options);
    }

    @Override
    protected void onResume() {
        super.onResume();
        StatusBarManager.setStatusBar(this, findViewById(R.id.v_status), true);
    }
}
