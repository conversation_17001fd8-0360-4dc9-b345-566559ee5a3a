package com.sayweee.weee.service;

import android.content.Context;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.sayweee.logger.Logger;
import com.sayweee.weee.R;
import com.sayweee.weee.global.App;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.DeepLinkManager;
import com.sayweee.weee.service.helper.NotificationHelper;
import com.sayweee.weee.service.notification.SimpleNotificationListener;
import com.urbanairship.AirshipConfigOptions;
import com.urbanairship.Autopilot;
import com.urbanairship.UAirship;
import com.urbanairship.channel.AirshipChannelListener;
import com.urbanairship.push.NotificationInfo;

public class MyAutopilot extends Autopilot {

    @Override
    public void onAirshipReady(@NonNull UAirship airship) {
        AccountManager.get().setPushChannelId(airship.getChannel().getId());
        //引导过默认开启推送能力
        NotificationHelper.checkUserNotificationsEnable();
        Logger.enable(DevConfig.isDebug()).v("onAirshipReady==>","Airship is ready " + airship.getChannel().getId(), airship.getPushManager().getUserNotificationsEnabled());
        airship.getPushManager().setNotificationListener(new SimpleNotificationListener() {
            @Override
            public boolean onNotificationOpened(@NonNull NotificationInfo notificationInfo) {
                final String pushUrl = notificationInfo.getMessage().getPushBundle().getString("url");
                Logger.enable(DevConfig.isDebug()).v("onAirshipNotificationOpened==>", "Notification opened. Alert: " + notificationInfo.getMessage().getAlert() + ". pushUrl: " + pushUrl);

                boolean processed = false;
                if (!TextUtils.isEmpty(pushUrl)) {
                    processed = DeepLinkManager.onReceivedUrl(App.application, pushUrl, Constants.SessionSource.SOURCE_NOTIFICATION, false);
                }
                return processed;
            }
        });

        airship.getChannel().addChannelListener(new AirshipChannelListener() {
            @Override
            public void onChannelCreated(@NonNull String channelId) {
                AccountManager.get().setPushChannelId(channelId);
                Logger.enable(DevConfig.isDebug()).v("onAirshipReady==>","Airship channel created " + channelId);
            }
        });
    }

    @Nullable
    @Override
    public AirshipConfigOptions createAirshipConfigOptions(@NonNull Context context) {
        int smallIcon;
        int accentColor;
        //pixel系统不支持推送icon有特殊要求
        boolean isPixelDevice = "google".equalsIgnoreCase(Build.BRAND);
        if (isPixelDevice) {
            smallIcon = R.mipmap.ic_icon_notification_pixel;
            accentColor = R.color.color_notification_icon_accent_pixel;
        } else {
            smallIcon = R.mipmap.ic_icon_notification_normal;
            accentColor = R.color.color_notification_icon_accent;
        }
        AirshipConfigOptions.Builder builder = new AirshipConfigOptions.Builder()
                .setInProduction(true)
                .setDevelopmentAppKey("QGPZKEYXTEiuEkQ8X3zqHQ")
                .setDevelopmentAppSecret("Fh290IdNRTueBy6Y5pr0aQ")
                .setProductionAppKey(ContextCompat.getString(context, R.string.airship_key))
                .setProductionAppSecret(ContextCompat.getString(context, R.string.airship_secret))
                .setDevelopmentLogLevel(Log.DEBUG)
                .setProductionLogLevel(Log.DEBUG)
                .setNotificationIcon(smallIcon)
                .setNotificationLargeIcon(R.mipmap.ic_icon_notification_large)
                .setNotificationAccentColor(ContextCompat.getColor(context, accentColor))
                .setUrlAllowList(new String[]{"*"});
        return builder.build();
    }
}