package com.sayweee.weee.service;

import androidx.annotation.NonNull;

import com.appsflyer.AppsFlyerLib;
import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;
import com.klaviyo.pushFcm.KlaviyoNotification;
import com.sayweee.logger.Logger;
import com.sayweee.weee.service.helper.PushHelper;
import com.urbanairship.push.fcm.AirshipFirebaseIntegration;

/**
 * Created by ycy on 2017/3/29.
 */
public class MyFirebaseMessagingService extends FirebaseMessagingService {
    @Override
    public void onMessageReceived(@NonNull RemoteMessage remoteMessage) {
        Logger.json("==> - onMessageReceived", remoteMessage);
        if (remoteMessage.getData().containsKey("af-uinstalluinstall-tracking")){
            return;//卸载推送通知静默推送
        }

        boolean isKlaviyoMessage = remoteMessage.getData().containsKey("_k");
        if (isKlaviyoMessage) {
            new KlaviyoNotification(remoteMessage).displayNotification(this);
            return;
        }

        // Also if you intend on generating your own notifications as a result of a received FCM
        // message, here is where that should be initiated. See sendNotification method below.
        AirshipFirebaseIntegration.processMessageSync(getApplicationContext(), remoteMessage);
    }

    @Override
    public void onNewToken(@NonNull String s) {
        super.onNewToken(s);
        Logger.json("==> - onNewToken", s);

        // Sending new token to AppsFlyer
        AppsFlyerLib.getInstance().updateServerUninstallToken(getApplicationContext(), s);

        // the rest of the code that makes use of the token goes in this method as well
        AirshipFirebaseIntegration.processNewToken(getApplicationContext(), s);
        PushHelper.safeSetKlaviyoPushToken(s);
    }

}
