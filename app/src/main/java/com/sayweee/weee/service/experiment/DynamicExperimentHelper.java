package com.sayweee.weee.service.experiment;

import com.sayweee.service.ConfigService;
import com.sayweee.weee.module.launch.service.ExperimentId;
import com.sayweee.weee.module.launch.service.ExperimentManager;
import com.sayweee.weee.service.config.bean.ExperimentConfigBean;
import com.sayweee.wrapper.core.compat.SimpleObserver;

/**
 * Author:  winds
 * Date:    2022/8/3.
 * Desc:
 */
public class DynamicExperimentHelper {

    public interface Key {
        String KEY_XP_SEARCH_BAR_BUTTON_VISIBLE = ExperimentId.DYN_SEARCH_BAR_BUTTON_VISIBLE;
    }

    public static void fetchResult(String key, SimpleObserver<Boolean> observer) {
        ExperimentConfigBean config = ConfigService.get().getDynamicConfig(key);
        if (config != null) {
            int experimentId = config.id;
            ConfigService.get().fetchExperiment(false, experimentId, ((result, failure) -> {
                if (result != null) {
                    observer.onNext(result.inExperiment());
                } else if (failure != null) {
                    Throwable exception = failure.getException() != null
                            ? failure.getException()
                            : new NullPointerException("exception is null");
                    observer.onError(exception);
                } else {
                    Throwable exception = new NullPointerException("failure is null");
                    observer.onError(exception);
                }
                observer.onComplete();
            }));
        } else {
            if (observer != null) {
                observer.onNext(false);
                observer.onComplete();
            }
        }
    }

}
