package com.sayweee.weee.service.notification;

import androidx.annotation.NonNull;

import com.urbanairship.push.NotificationActionButtonInfo;
import com.urbanairship.push.NotificationInfo;
import com.urbanairship.push.NotificationListener;

/**
 * Author:  winds
 * Date:    11/23/21.
 * Desc:
 */
public abstract class SimpleNotificationListener implements NotificationListener {
    @Override
    public void onNotificationPosted(@NonNull NotificationInfo notificationInfo) {

    }

    @Override
    public boolean onNotificationOpened(@NonNull NotificationInfo notificationInfo) {
        return false;
    }

    @Override
    public boolean onNotificationForegroundAction(@NonNull NotificationInfo notificationInfo, @NonNull NotificationActionButtonInfo actionButtonInfo) {
        return false;
    }

    @Override
    public void onNotificationBackgroundAction(@NonNull NotificationInfo notificationInfo, @NonNull NotificationActionButtonInfo actionButtonInfo) {

    }

    @Override
    public void onNotificationDismissed(@NonNull NotificationInfo notificationInfo) {

    }
}
