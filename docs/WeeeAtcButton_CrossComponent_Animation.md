# WeeeAtcButton 跨组件动画同步解决方案

## 问题描述

在RN页面中使用多个WeeeAtcButton组件时，需要实现"展开当前组件时，前面一个展开的收起来"的功能，类似于原生CartOpLayout在列表中的行为。

## 解决方案概述

通过集成`SharedOrderViewModel`实现跨组件状态同步，当一个WeeeAtcButton展开时，会通知其他已展开的组件自动收起。

## 核心实现

### 1. SharedOrderViewModel集成

在`AtcButtonProvider.java`中添加了`SharedOrderViewModel`监听器：

```java
private void setupSharedViewModelListener(ThemedReactContext context) {
    SharedOrderViewModel sharedViewModel = SharedOrderViewModel.get();
    sharedViewModel.productOpStatusData.observe((LifecycleOwner) activity, new Observer<Integer>() {
        @Override
        public void onChanged(Integer expandedProductId) {
            if (expandedProductId != null && operationListener != null && cartOpLayout != null) {
                int currentProductId = operationListener.getProductId();
                
                // 如果其他产品展开且当前组件已展开，则收起当前组件
                if (currentProductId != expandedProductId && 
                    cartOpLayout.getOpStatus() == CartOpLayout.OP_STATUS_EXPAND) {
                    cartOpLayout.collapseWithAnim();
                    operationListener.onUpdate();
                }
            }
        }
    });
}
```

### 2. 展开通知机制

在用户操作时通知`SharedOrderViewModel`：

```java
private void notifyExpansion() {
    if (operationListener != null) {
        int productId = operationListener.getProductId();
        SharedOrderViewModel sharedViewModel = SharedOrderViewModel.get();
        sharedViewModel.productOpStatusData.postValue(productId);
    }
}
```

### 3. 新增expandWithNotification方法

为WeeeAtcButton添加了手动触发展开的方法：

```kotlin
override fun expandWithNotification() {
    productData?.let { data ->
        val sharedViewModel = com.sayweee.core.order.SharedOrderViewModel.get()
        sharedViewModel.productOpStatusData.postValue(data.id)
    }
}
```

## 使用方法

### 1. 基本使用

```tsx
import {WeeeAtcButton} from 'weee-native';

const ProductItem = ({product}) => {
  const [quantity, setQuantity] = useState(0);
  
  const handleAtcEvent = (type: string, qty: number) => {
    switch (type) {
      case 'update':
        setQuantity(qty);
        break;
      case 'collapse':
        console.log('组件收起，数量:', qty);
        break;
    }
  };

  return (
    <WeeeAtcButton
      productJson={JSON.stringify(product)}
      onUpdate={handleAtcEvent}
      style={{width: 120, height: 40}}
    />
  );
};
```

### 2. 手动控制展开/收起

```tsx
const ProductList = ({products}) => {
  const atcButtonRefs = useRef({});

  const expandProduct = (productId) => {
    const atcRef = atcButtonRefs.current[productId];
    if (atcRef) {
      // 展开当前产品，其他已展开的会自动收起
      atcRef.expandWithNotification();
    }
  };

  const collapseProduct = (productId) => {
    const atcRef = atcButtonRefs.current[productId];
    if (atcRef) {
      atcRef.collapse();
    }
  };

  return (
    <FlatList
      data={products}
      renderItem={({item}) => (
        <WeeeAtcButton
          ref={(ref) => {
            atcButtonRefs.current[item.id] = ref;
          }}
          productJson={JSON.stringify(item)}
          onUpdate={(type, qty) => {
            // 处理事件
          }}
        />
      )}
    />
  );
};
```

## 动画支持

### RN列表同时渲染多个动画

React Native可以同时渲染多个动画，但需要注意：

1. **性能优化**: 避免同时运行过多复杂动画
2. **动画时序**: 收起动画和展开动画可以同时进行
3. **状态管理**: 正确管理组件状态避免冲突

### 动画时序

```
用户点击产品A的ATC按钮
    ↓
1. 通知SharedOrderViewModel (产品A展开)
    ↓
2. 其他已展开的组件收到通知并开始收起动画
    ↓
3. 产品A开始展开动画
    ↓
4. 收起和展开动画同时进行 (150ms)
    ↓
5. 动画完成，状态更新
```

## 事件回调

WeeeAtcButton支持以下事件类型：

- `update`: 组件状态更新（展开/收起/数量变化）
- `collapse`: 组件收起时触发，包含最终数量

```tsx
const handleAtcEvent = (type: string, qty: number) => {
  switch (type) {
    case 'update':
      // 处理状态更新
      setQuantity(qty);
      if (qty > 0) {
        setIsExpanded(true);
      } else {
        setIsExpanded(false);
      }
      break;
      
    case 'collapse':
      // 处理收起事件
      console.log('组件收起，最终数量:', qty);
      setIsExpanded(false);
      break;
  }
};
```

## 注意事项

1. **生命周期管理**: SharedOrderViewModel监听器会在组件attach/detach时自动管理
2. **内存泄漏**: 组件销毁时会自动清理监听器
3. **异常处理**: 所有SharedOrderViewModel操作都有异常捕获
4. **性能考虑**: 避免在短时间内频繁触发展开/收起操作

## 示例项目

完整的示例代码请参考：
- `src/component/product/AtcButtonList.tsx` - 列表组件示例
- `src/screens/AtcButtonDemo.tsx` - 演示页面

## 技术细节

### 依赖注入架构

```
WeeeAtcButton (Nitro Module)
    ↓
IAtcButtonProvider (Interface)
    ↓
AtcButtonProvider (Implementation)
    ↓
CartOpLayout (Native Component)
    ↓
SharedOrderViewModel (State Management)
```

这种架构确保了：
- 模块间解耦
- 易于测试和维护
- 支持不同实现方式

### 状态同步流程

1. 用户操作触发CartOpLayout事件
2. AtcButtonProvider包装并转发事件
3. 同时通知SharedOrderViewModel状态变化
4. 其他组件监听到状态变化并响应
5. 执行相应的动画和状态更新
