# WeeeAtcButton 跨组件动画同步解决方案

## 问题描述

在RN页面中使用多个WeeeAtcButton组件时，需要实现"展开当前组件时，前面一个展开的收起来"的功能，类似于原生CartOpLayout在列表中的行为。

## 解决方案概述

利用CartOpLayout内置的`SharedOrderViewModel`监听机制，在正确的时机触发`SharedOrderViewModel.productOpStatusData.postValue(productId)`来实现跨组件状态同步。

## 核心原理

CartOpLayout已经内置了完整的跨组件同步机制：

1. **自动监听**：CartOpLayout在`onAttachedToWindow()`时自动监听`SharedOrderViewModel.productOpStatusData`
2. **自动收起**：当接收到其他productId的展开通知时，如果当前组件处于展开状态，会自动调用`collapseWithAnim()`
3. **状态管理**：通过`getOpStatus()`可以检查组件当前状态（OP_STATUS_EXPAND/OP_STATUS_COLLAPSE）

## 核心实现

### 1. 利用CartOpLayout内置机制

CartOpLayout已经内置了完整的SharedOrderViewModel监听机制，我们无需重复实现：

```java
// CartOpLayout.java - 内置实现
@Override
protected void onAttachedToWindow() {
    super.onAttachedToWindow();
    LifecycleOwner lifecycleOwner = ViewTreeLifecycleOwner.get(this);
    if (lifecycleOwner != null) {
        SharedOrderViewModel sharedViewModel = SharedOrderViewModel.get();
        if (sharedViewModel != null) {
            sharedViewModel.productOpStatusData.observe(lifecycleOwner, this);
        }
    }
}

@Override
public void onChanged(Integer integer) {
    if (listener != null && integer != null && opStatus == OP_STATUS_EXPAND) {
        int productId = listener.getProductId();
        if (productId != integer) {
            collapseWithAnim(); // 自动收起其他已展开的组件
        }
    }
}
```

### 2. 正确的展开通知时机

在用户操作完成后通知SharedOrderViewModel：

```java
@Override
public void operateRight(View view) {
    if (listener != null) {
        listener.operateRight(view); // 先执行原始操作
    }

    // 在操作完成后检查状态并通知
    cartOpLayout.post(() -> {
        if (cartOpLayout.getOpStatus() == CartOpLayout.OP_STATUS_EXPAND) {
            notifyExpansion(); // 通知其他组件收起
        }
    });
}
```

### 3. 手动展开方法

为WeeeAtcButton添加了手动触发展开的方法：

```java
public void expandWithNotification() {
    if (cartOpLayout != null && operationListener != null) {
        // 获取当前数量并+1来触发展开
        int currentQty = cartOpLayout.getTextNum();
        int newQty = Math.max(1, currentQty + 1);

        // 使用setTextNumAnim触发展开动画
        cartOpLayout.setTextNumAnim(newQty);

        // 通知其他组件收起
        cartOpLayout.post(() -> {
            if (cartOpLayout.getOpStatus() == CartOpLayout.OP_STATUS_EXPAND) {
                notifyExpansion();
            }
        });
    }
}
```

## 使用方法

### 1. 基本使用

```tsx
import {WeeeAtcButton} from 'weee-native';

const ProductItem = ({product}) => {
  const [quantity, setQuantity] = useState(0);

  const handleAtcEvent = (type: string, qty: number) => {
    console.log(`Product ${product.id}: ${type}, qty=${qty}`);

    switch (type) {
      case 'update':
        setQuantity(qty);
        break;
      case 'collapse':
        console.log('组件收起，最终数量:', qty);
        setQuantity(qty);
        break;
    }
  };

  // 构建产品数据
  const productData = {
    id: product.id,
    name: product.name,
    price: product.price,
    product_key: product.product_key,
    min_order_quantity: 1,
    max_order_quantity: 99,
    volume_threshold: 3,
    // ... 其他必要字段
  };

  return (
    <WeeeAtcButton
      productJson={JSON.stringify(productData)}
      onUpdate={handleAtcEvent}
      style={{width: 120, height: 40}}
    />
  );
};
```

### 2. 手动控制展开/收起

```tsx
const ProductList = ({products}) => {
  const atcButtonRefs = useRef({});

  const expandProduct = (productId) => {
    const atcRef = atcButtonRefs.current[productId];
    if (atcRef) {
      console.log(`手动展开产品 ${productId}`);
      // 展开当前产品，其他已展开的会自动收起
      atcRef.expandWithNotification();
    }
  };

  const collapseProduct = (productId) => {
    const atcRef = atcButtonRefs.current[productId];
    if (atcRef) {
      console.log(`手动收起产品 ${productId}`);
      atcRef.collapse();
    }
  };

  return (
    <View>
      {/* 控制按钮 */}
      <View style={{flexDirection: 'row', justifyContent: 'space-around'}}>
        <TouchableOpacity onPress={() => expandProduct(products[0]?.id)}>
          <Text>展开第一个</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => collapseProduct(products[0]?.id)}>
          <Text>收起第一个</Text>
        </TouchableOpacity>
      </View>

      {/* 产品列表 */}
      <FlatList
        data={products}
        renderItem={({item}) => (
          <WeeeAtcButton
            ref={(ref) => {
              atcButtonRefs.current[item.id] = ref;
            }}
            productJson={JSON.stringify(item)}
            onUpdate={(type, qty) => {
              console.log(`Product ${item.id}: ${type}, qty=${qty}`);
            }}
          />
        )}
      />
    </View>
  );
};
```

## 动画支持

### RN列表同时渲染多个动画

React Native可以同时渲染多个动画，但需要注意：

1. **性能优化**: 避免同时运行过多复杂动画
2. **动画时序**: 收起动画和展开动画可以同时进行
3. **状态管理**: 正确管理组件状态避免冲突

### 动画时序

```
用户点击产品A的ATC按钮
    ↓
1. 通知SharedOrderViewModel (产品A展开)
    ↓
2. 其他已展开的组件收到通知并开始收起动画
    ↓
3. 产品A开始展开动画
    ↓
4. 收起和展开动画同时进行 (150ms)
    ↓
5. 动画完成，状态更新
```

## 事件回调

WeeeAtcButton支持以下事件类型：

- `update`: 组件状态更新（展开/收起/数量变化）
- `collapse`: 组件收起时触发，包含最终数量

```tsx
const handleAtcEvent = (type: string, qty: number) => {
  switch (type) {
    case 'update':
      // 处理状态更新
      setQuantity(qty);
      if (qty > 0) {
        setIsExpanded(true);
      } else {
        setIsExpanded(false);
      }
      break;
      
    case 'collapse':
      // 处理收起事件
      console.log('组件收起，最终数量:', qty);
      setIsExpanded(false);
      break;
  }
};
```

## 注意事项

1. **生命周期管理**: SharedOrderViewModel监听器会在组件attach/detach时自动管理
2. **内存泄漏**: 组件销毁时会自动清理监听器
3. **异常处理**: 所有SharedOrderViewModel操作都有异常捕获
4. **性能考虑**: 避免在短时间内频繁触发展开/收起操作

## 示例项目

完整的示例代码请参考：
- `src/component/product/AtcButtonList.tsx` - 列表组件示例
- `src/screens/AtcButtonDemo.tsx` - 演示页面

## 技术细节

### 依赖注入架构

```
WeeeAtcButton (Nitro Module)
    ↓
IAtcButtonProvider (Interface)
    ↓
AtcButtonProvider (Implementation)
    ↓
CartOpLayout (Native Component)
    ↓
SharedOrderViewModel (State Management)
```

这种架构确保了：
- 模块间解耦
- 易于测试和维护
- 支持不同实现方式

### 状态同步流程

1. 用户操作触发CartOpLayout事件
2. AtcButtonProvider包装并转发事件
3. 同时通知SharedOrderViewModel状态变化
4. 其他组件监听到状态变化并响应
5. 执行相应的动画和状态更新
