# WeeeAtcButton 跨组件动画同步实现总结

## 问题分析

原始问题：在RN列表中使用多个WeeeAtcButton时，需要实现"展开当前组件时，前面一个展开的收起来"的功能。

## 关键发现

1. **CartOpLayout内置机制**：CartOpLayout已经内置了完整的跨组件同步机制
2. **SharedOrderViewModel**：通过`SharedOrderViewModel.productOpStatusData`实现全局状态管理
3. **自动监听**：CartOpLayout在attach到window时自动监听SharedOrderViewModel
4. **自动收起**：当接收到其他productId的展开通知时，已展开的组件会自动收起

## 核心修改

### 1. AtcButtonProvider.java

**关键修改点**：
- 移除了重复的SharedOrderViewModel监听器（CartOpLayout已内置）
- 在用户操作完成后的正确时机通知SharedOrderViewModel
- 添加了`expandWithNotification()`方法支持手动展开

```java
// 在操作完成后检查状态并通知
@Override
public void operateRight(View view) {
    if (listener != null) {
        listener.operateRight(view); // 先执行原始操作
    }
    
    // 在操作完成后检查状态并通知
    cartOpLayout.post(() -> {
        if (cartOpLayout.getOpStatus() == CartOpLayout.OP_STATUS_EXPAND) {
            notifyExpansion(); // 通知其他组件收起
        }
    });
}

// 手动展开方法
public void expandWithNotification() {
    if (cartOpLayout != null && operationListener != null) {
        int currentQty = cartOpLayout.getTextNum();
        int newQty = Math.max(1, currentQty + 1);
        
        // 使用setTextNumAnim触发展开动画
        cartOpLayout.setTextNumAnim(newQty);
        
        // 通知其他组件收起
        cartOpLayout.post(() -> {
            if (cartOpLayout.getOpStatus() == CartOpLayout.OP_STATUS_EXPAND) {
                notifyExpansion();
            }
        });
    }
}
```

### 2. WeeeAtcButton.kt

**关键修改点**：
- 实现了`expandWithNotification()`方法
- 委托给AtcButtonProvider处理具体逻辑
- 改进了事件回调处理

```kotlin
override fun expandWithNotification() {
    try {
        uiComponent.expandWithNotification()
        Log.d(TAG, "Triggered expansion via UI component")
    } catch (e: Exception) {
        Log.w(TAG, "Failed to trigger expansion", e)
    }
}
```

### 3. TypeScript接口更新

**关键修改点**：
- 在`WeeeAtcButton.nitro.ts`中添加了`expandWithNotification()`方法
- 重新生成了nitrogen代码

```typescript
export interface AtcButtonMethods extends HybridViewMethods {
  collapse(): void
  expandWithNotification(): void
}
```

## 工作流程

### 用户点击操作流程

```
用户点击ATC按钮
    ↓
CartOpLayout处理点击事件
    ↓
OpHelper.helperOp执行业务逻辑
    ↓
CartOpLayout状态更新（展开/收起）
    ↓
AtcButtonProvider检查状态
    ↓
如果是展开状态，通知SharedOrderViewModel
    ↓
其他CartOpLayout接收通知并自动收起
    ↓
动画同时执行（收起+展开）
```

### 手动展开流程

```
调用expandWithNotification()
    ↓
AtcButtonProvider.expandWithNotification()
    ↓
cartOpLayout.setTextNumAnim(newQty)
    ↓
触发展开动画
    ↓
通知SharedOrderViewModel
    ↓
其他组件自动收起
```

## 测试验证

### 1. 基本功能测试

使用`AtcButtonTestScreen.tsx`进行测试：
- 手动展开不同产品
- 验证其他已展开的组件是否自动收起
- 检查动画是否正常播放

### 2. 日志监控

通过控制台日志监控：
- 组件状态变化
- SharedOrderViewModel通知
- 动画执行情况

## 关键注意事项

### 1. 时序问题

**重要**：必须在CartOpLayout状态更新完成后再通知SharedOrderViewModel，否则可能导致状态不一致。

```java
// 错误的做法
notifyExpansion(); // 在操作前通知
listener.operateRight(view);

// 正确的做法
listener.operateRight(view); // 先执行操作
cartOpLayout.post(() -> { // 在下一个UI循环中检查状态
    if (cartOpLayout.getOpStatus() == CartOpLayout.OP_STATUS_EXPAND) {
        notifyExpansion();
    }
});
```

### 2. 状态检查

使用`cartOpLayout.getOpStatus()`而不是数量来判断展开状态，因为CartOpLayout可能处于展开状态但数量为0。

### 3. 生命周期管理

CartOpLayout的SharedOrderViewModel监听器会在attach/detach时自动管理，无需手动处理。

## 文件清单

### 修改的文件
- `app/src/main/java/com/sayweee/weee/react/atc/AtcButtonProvider.java`
- `modules/weee-native/android/src/main/java/com/margelo/nitro/weee/WeeeAtcButton.kt`
- `modules/weee-native/android/src/main/java/com/margelo/nitro/weee/service/IAtcButtonProvider.kt`
- `modules/weee-native/src/nitro/WeeeAtcButton.nitro.ts`

### 新增的文件
- `src/component/product/AtcButtonList.tsx` - 列表组件示例
- `src/screens/AtcButtonDemo.tsx` - 演示页面
- `src/screens/AtcButtonTestScreen.tsx` - 测试页面
- `docs/WeeeAtcButton_CrossComponent_Animation.md` - 详细文档

### 生成的文件
- `modules/weee-native/nitrogen/generated/android/kotlin/com/margelo/nitro/weee/HybridWeeeAtcButtonSpec.kt`

## 总结

通过正确利用CartOpLayout内置的SharedOrderViewModel机制，我们成功实现了WeeeAtcButton的跨组件动画同步功能。关键是在正确的时机触发SharedOrderViewModel通知，而不是重复实现监听机制。

这个解决方案：
- ✅ 完全复用了CartOpLayout的现有机制
- ✅ 支持手动和自动展开/收起
- ✅ 支持同时播放多个动画
- ✅ 保持了与原生CartOpLayout一致的行为
- ✅ 具有良好的性能和稳定性
