/**
 * @format
 */

import { AppRegistry } from 'react-native';
import App from './App';
import { name as appName } from './app.json';

import 'globalcss'

import withAppInitializer from "@/init";

import MyListPage from '@/page/account/my-list';

import AtcButtonDemo from "@/screens/AtcButtonDemo";

// AppRegistry.registerComponent('account/my-list', () => withAppInitializer(MyListPage));
AppRegistry.registerComponent('account/my-list', () => AtcButtonDemo);

AppRegistry.registerComponent(appName, () => App);

